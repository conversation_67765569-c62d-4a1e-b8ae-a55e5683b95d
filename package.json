{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "^1.3.9", "@nuxt/image": "^1.7.0", "@nuxtjs/tailwindcss": "^6.12.1", "daisyui": "^4.12.10", "nuxt": "^3.12.4", "nuxt-icon": "^0.6.10", "shadcn-nuxt": "^0.10.4", "typescript": "^5.5.4", "vue": "^3.4.38", "vue-router": "^4.4.3"}, "dependencies": {"@pinia/nuxt": "^0.5.3", "@tanstack/vue-table": "^8.20.4", "@types/luxon": "^3.4.2", "@types/papaparse": "^5.3.15", "@unovis/ts": "^1.4.4-beta.2", "@unovis/vue": "^1.4.4-beta.2", "@vee-validate/zod": "^4.13.2", "@vueuse/core": "^10.11.1", "apexcharts": "^3.52.0", "axios": "^1.7.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.427.0", "luxon": "^3.5.0", "pdf-lib": "^1.17.1", "pinia": "^2.2.1", "radix-vue": "^1.9.4", "reka-ui": "^2.1.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "v-calendar": "^3.1.2", "vee-validate": "^4.13.2", "vue-sonner": "^1.3.0", "vue3-apexcharts": "^1.5.3", "zod": "^3.23.8"}}