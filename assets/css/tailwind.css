@tailwind base;
@tailwind components;
@tailwind utilities;
 


@layer base {
  
:root  {
  --background: 261 100% 95%;
  --foreground: 261 5% 0%;
  --card: 261 50% 90%;
  --card-foreground: 261 5% 10%;
  --popover: 261 100% 95%;
  --popover-foreground: 261 100% 0%;
  --primary: 261 51.1% 55.9%;
  --primary-foreground: 0 0% 100%;
  --secondary: 261 30% 70%;
  --secondary-foreground: 0 0% 0%;
  --muted: 223 30% 85%;
  --muted-foreground: 261 5% 35%;
  --accent: 223 30% 80%;
  --accent-foreground: 261 5% 10%;
  --destructive: 0 100% 30%;
  --destructive-foreground: 261 5% 90%;
  --border: 261 30% 50%;
  --input: 261 30% 18%;
  --ring: 261 51.1% 55.9%;
  --radius: 0.5rem;
}
.dark  {
  --background: 261 50% 5%;
  --foreground: 261 5% 90%;
  --card: 261 50% 0%;
  --card-foreground: 261 5% 90%;
  --popover: 261 50% 5%;
  --popover-foreground: 261 5% 90%;
  --primary: 261 51.1% 55.9%;
  --primary-foreground: 0 0% 100%;
  --secondary: 261 30% 10%;
  --secondary-foreground: 0 0% 100%;
  --muted: 223 30% 15%;
  --muted-foreground: 261 5% 60%;
  --accent: 223 30% 15%;
  --accent-foreground: 261 5% 90%;
  --destructive: 0 100% 30%;
  --destructive-foreground: 261 5% 90%;
  --border: 261 30% 18%;
  --input: 261 30% 18%;
  --ring: 261 51.1% 55.9%;
  --radius: 0.5rem;
}

}

