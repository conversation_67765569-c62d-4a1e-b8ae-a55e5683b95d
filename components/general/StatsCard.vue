<template>
       <!-- Stats Cards -->
       <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div v-for="stat in stats" :key="stat.title" class="bg-white rounded-xl p-4 space-y-2">
                    <div class="flex items-center gap-2">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <component :is="stat.icon" class="w-5 h-5 text-purple-600" />
                        </div>
                        <span class="text-sm text-gray-600">{{ stat.title }}</span>
                    </div>
                    <div class="text-2xl font-bold">
                        <template v-if="isLoading">
                            <div class="animate-pulse h-8 w-16 bg-gray-200 rounded"></div>
                        </template>
                        <template v-else>
                            {{ stat.value }}
                        </template>
                    </div>
                </div>
            </div>
</template>

<script setup lang="ts">


export interface StatItem {
    icon: any;
    value: string;
    title: string;
}

defineProps({
    stats: {
        type: Array as () => StatItem[],
        required: true,
        default: () => []
    },
    isLoading: {
        type: Boolean,
        default: false
    }
});
</script>