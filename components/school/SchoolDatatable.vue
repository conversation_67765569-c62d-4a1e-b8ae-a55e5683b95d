<template>
    <DataTable :count="schoolData?.count ?? 0" :columns="columns" :data="schoolData?.data ?? []" :is-loading="schoolStatus == 'pending'" @get-table-data="handleDataTableData" @get-row-data="handleRowClicked">
        <template #dataTableSearch>
            <div class="bg-white rounded-xl p-4 w-1/2">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <Input v-if="dataTableRef" type="search"
                            placeholder="Search school name..."
                            :model-value="(dataTableRef.getColumn('name')?.getFilterValue() as string) ?? ''"
                            @input="dataTableRef.getColumn('name')?.setFilterValue($event.target.value)" />
                    </div>
                </div>
            </div>
        </template>

        <template #dataTableActions v-if="schoolStore.selectedSchools.length > 0">
            <div class="ml-5 flex flex-col ">
                <p class="text-muted-foreground text-sm mb-2">Quick Actions</p>
                <div class="flex items-center gap-2">
                    <Button @click="deleteSchools()" variant="outline" size="sm"
                    class=" h-8 border-dashed border-red-500 text-red-500 bg-red-100">
                    <Trash class="mr-2 h-4 w-4 text-red-500" />
                    Delete {{ schoolStore.selectedSchools.length }} schools
                </Button>

                <Button @click=" schoolStore.exportToCSV()" variant="outline" size="sm"
                    class=" h-8 border-dashed border-blue-500 text-primary-500 bg-blue-100">
                    <Download class="mr-2 h-4 w-4 text-blue-500" />
                    Export {{ schoolStore.selectedSchools.length }} schools
                </Button>
                </div>
               
            </div>

        </template>
    </DataTable>

    <AlertDialog :open="isMultipleDeleteAlertOpen" @update:open="isMultipleDeleteAlertOpen = $event">
        <AlertDialogContent>
            <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete <span class="text-black font-bold">{{
        schoolStore.selectedSchools.length }} schools</span> and all associated data including
                    children and screenings. This action is irreversible.
                </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
                <AlertDialogCancel @click="isMultipleDeleteAlertOpen = false">Cancel</AlertDialogCancel>
                <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    @click="handleMultipleDelete" :disabled="deleteStatus === 'pending'">
                    <Loader2 v-if="deleteStatus === 'pending'" class="mr-2 h-4 w-4 animate-spin" />
                    {{ deleteStatus === 'pending' ? 'Deleting...' : `Yes, Delete
                    ${schoolStore.selectedSchools.length}
                    Schools` }}
                </AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
</template>

<script setup lang="ts">
import { type Table, type Row, type ColumnDef } from '@tanstack/vue-table'
import { Building2, Trash, Download } from 'lucide-vue-next'
import { useSchoolsStore, type School } from '~/stores/schools/schools.store'
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/toast/use-toast'

const schoolStore = useSchoolsStore()
const toast = useToast()

const currentPage = ref(1)

// Fetch school list with pagination
const { data: schoolData, status: schoolStatus, refresh: refreshSchool } = useAsyncData(
    'schools-list',
    () => schoolStore.fetchAllSchools(currentPage.value),
    {
        lazy: true,
        watch: [currentPage]
    }
)


// DATA TABLE DATA
const dataTableRef = ref<Table<School>>()
const selectedRow = ref<Row<School>>()
const handleDataTableData = (data: Table<School>) => {
    dataTableRef.value = data
}
const handleRowClicked = async (row: Row<School>) => {
    selectedRow.value = row
}

const columns: ColumnDef<School>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllRowsSelected(),
            'onUpdate:checked': value => {
                table.toggleAllRowsSelected(!!value);
                schoolStore.selectSchools(dataTableRef.value?.getFilteredSelectedRowModel().rows.map(row => row.original) ?? []);

            },
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => {
                row.toggleSelected(!!value)
                schoolStore.selectSchools(dataTableRef.value?.getFilteredSelectedRowModel().rows.map(row => row.original) ?? []);
            },
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'name',
        header: 'School Name',
        cell: ({ row }) => h('div', { class: 'flex items-center gap-3' }, [
            h('div', { class: 'p-2 bg-purple-100 rounded-lg' }, [
                h(Building2, { class: 'w-5 h-5 text-purple-600' })
            ]),
            h('div', { class: 'font-medium' }, row.original.name)
        ]),
        filterFn: (row, id, value) => {
            return row.original.name.toLowerCase().includes(value.toLowerCase())
        }
    },
    {
        accessorKey: 'country',
        header: 'Country',
        cell: ({ row }) => row.original.country?.name || ''
    },
    {
        accessorKey: 'district',
        header: 'District',
        cell: ({ row }) => row.original.district?.name || ''
    },
    {
        accessorKey: 'type',
        header: 'Type',
        cell: ({ row }) => row.original.type?.name || ''
    },
    {
        accessorKey: 'total_children_count',
        header: 'Total Students',
        cell: ({ row }) => row.original.total_children_count
    },
    // {
    //     id: 'screening_rate',
    //     header: 'Screening Rate',
    //     cell: ({ row }) => h('div', { class: 'flex items-center gap-2' }, [
    //         h('div', { class: 'w-24 bg-gray-100 rounded-full h-2' }, [
    //             h('div', {
    //                 class: 'bg-purple-600 h-2 rounded-full',
    //                 style: { width: `${(0/row.original.total_children_count) * 100}%` }
    //             })
    //         ]),
    //         h('span', {}, `${((0/row.original.total_children_count) * 100).toFixed(1)}%`)
    //     ])
    // }
]

//SINGLE DELETE ALERT DIALOGUE
const isAlertDialogOpen = ref(false)

const deleteStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle')
const handleDelete = async () => {
    if (!schoolStore.selectedSchool?.id) return

    deleteStatus.value = 'pending'
    const { error, status } = await useAsyncData(
        'delete-school',
        () => schoolStore.deleteSchool(schoolStore.selectedSchool!.id)
    )

    if (error.value) {
        deleteStatus.value = 'error'
        toast.toast({
            title: "Error",
            description: "Failed to delete school",
            variant: "destructive",
        })
    } else {
        deleteStatus.value = 'success'
        toast.toast({
            title: "Success",
            description: `${schoolStore.selectedSchool!.name} has been deleted successfully`,
        })
        // Emit the deleted school's ID
        emit('on-school-deleted', schoolStore.selectedSchool!.id)
        refreshSchool()
    }

    isAlertDialogOpen.value = false
}

//MULTIPLE DELETE ALERT DIALOGUE
const isMultipleDeleteAlertOpen = ref(false)
const handleMultipleDelete = async () => {
    if (!schoolStore.selectedSchools.length) return
    deleteStatus.value = 'pending'
    const { error } = await useAsyncData(
        'delete-schools',
        () => schoolStore.deleteMultipleSchools(schoolStore.selectedSchools.map(school => school.id))
    )
    if (error.value) {
        deleteStatus.value = 'error'
        toast.toast({
            title: "Error",
            description: "Failed to delete schools",
            variant: "destructive",
        })
    } else {
        deleteStatus.value = 'success'
        toast.toast({
            title: "Success",
            description: `${schoolStore.selectedSchools.length} schools have been deleted successfully`,
        })
        // Emit the deleted school's ID
        emit('on-school-deleted', "")
        refreshSchool()

        // Reset the selected rows
        dataTableRef.value?.setRowSelection([] as any)
        schoolStore.resetSelectedSchools()
    }
    isMultipleDeleteAlertOpen.value = false
}

const emit = defineEmits<{
    'on-school-deleted': [schoolId: string]
}>();

const deleteSchools = () => {
    isMultipleDeleteAlertOpen.value = true
}
</script>