<script setup lang="ts">
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/avatar'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import {
  BadgeCheck,
  Bell,
  Building2,
  ChevronsUpDown,
  Loader2,
  LogOut,
} from 'lucide-vue-next'
import type { User } from '~/stores/auth/user/model/user.model';
import { useAuthStore } from '~/stores/auth/auth.store';

const props = defineProps<{
  user: User
}>()

const { isMobile } = useSidebar()

const authStore = useAuthStore()

const isLoggingOut = ref(false)
const handleLogout = async () => {
  isLoggingOut.value = true
  authStore.logoutUser()
  await useRouter().go(0) // Refresh the page after logout
}

// Add to script setup
const showLogoutDialog = ref(false)
const openLogoutDialog = () => {
  showLogoutDialog.value = true
}
</script>

<template>
  <SidebarMenu>
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage :src="user.avatarUrl ?? ''" :alt="user.firstName" />
              <AvatarFallback class="rounded-lg">
                CN
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">{{ user.firstName }}</span>
              <span class="truncate text-xs">{{ user.email }}</span>
            </div>
            <ChevronsUpDown class="ml-auto size-4" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          :side="isMobile ? 'bottom' : 'right'"
          align="end"
          :side-offset="4"
        >
          <DropdownMenuLabel class="p-0 font-normal">
            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
              <Avatar class="h-8 w-8 rounded-lg">
                <AvatarImage :src="user.avatarUrl ?? ''" :alt="user.firstName" />
                <AvatarFallback class="rounded-lg">
                  CN
                </AvatarFallback>
              </Avatar>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">{{ user.firstName }} {{ user.lastName }}</span>
                <span class="truncate text-xs">{{ user.email }}</span>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem class="cursor-default">
              <Building2 class="mr-2 h-4 w-4 text-purple-600" />
              <div class="flex flex-col">
                <span class="text-sm font-medium">{{ user.organization?.name ?? 'No Organization' }}</span>
                <span class="text-xs text-muted-foreground">{{ user.role?.name ?? 'Member' }}</span>
              </div>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem>
              <BadgeCheck />
              Account
            </DropdownMenuItem>
         
            <DropdownMenuItem>
              <Bell />
              Notifications
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          
              <DropdownMenuItem @click="openLogoutDialog">
                <LogOut class="mr-2 h-4 w-4" />
                Log out
              </DropdownMenuItem>
         
            </DropdownMenuContent>
            </DropdownMenu>
            </SidebarMenuItem>
            </SidebarMenu>
            
            <!-- Add AlertDialog outside the SidebarMenu -->
            <AlertDialog :open="showLogoutDialog" @update:open="showLogoutDialog = $event">
            <AlertDialogContent>
            <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to log out?</AlertDialogTitle>
            <AlertDialogDescription>
            You will need to sign in again to access your account and screening data.
            </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
            <AlertDialogCancel @click="showLogoutDialog = false">Cancel</AlertDialogCancel>
            <AlertDialogAction 
              class="bg-purple-600 hover:bg-purple-700" 
              @click="handleLogout"
              :disabled="isLoggingOut"
            >
              <Loader2 v-if="isLoggingOut" class="mr-2 h-4 w-4 animate-spin" />
              {{ isLoggingOut ? 'Logging out...' : 'Log out' }}
            </AlertDialogAction>
            </AlertDialogFooter>
            </AlertDialogContent>
            </AlertDialog>
            </template>
