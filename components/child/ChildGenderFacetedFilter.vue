<template>
    <DataTableFacetedFilter title="Gender" :options="genderFacetedData" @handle-popover-open="handlePopoverOpen"
        @handle-filter="handleFilter">
    </DataTableFacetedFilter>
</template>
<script setup lang="ts">
import { type Table } from '@tanstack/vue-table'
import type { DataTableFacatedFilterOptions } from '../DataTableFacetedFilter.vue';
import {  type Child } from '~/stores/children/children.store'


interface TableProps {
    table: Table<Child>
}

const props = defineProps<TableProps>()
const emit = defineEmits(['handleFilter'])

const genderTypes = [
    {
        label: "Male",
        value: "Male"
    },
    {
        label: "Female",
        value: "Female"
    }
]

const handlePopoverOpen = (state: boolean) => {

}

const handleFilter = (filteredValues: DataTableFacatedFilterOptions[]) => {

    emit('handleFilter', filteredValues)
}

const genderFacetedData = computed(() => genderTypes)


</script>