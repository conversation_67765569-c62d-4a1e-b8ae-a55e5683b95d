<template>
    <DataTable :current-page="currentPage" :count="childrenData?.count ?? 0" :columns="columns"
        :data="childrenData?.data ?? []" :is-loading="childrenStatus == 'pending'" @get-table-data="handleDataTableData"
        @set-page-index="handlePageIndex" @get-row-data="handleRowClicked" @next-page="handleNextPage"
        @previous-page="handlePreviousPage">
        <template #dataTableSearch>
            <div class="bg-white rounded-xl p-4 w-1/2">

                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <Input type="search" placeholder="Search child name..." @input="search($event.target.value)" />
                    </div>
                </div>
            </div>
        </template>

        <template #dataTableActions v-if="childrenStore.allChildrenSelected || childrenStore.selectedChildren.length > 0">
            <div class="ml-5 flex flex-col ">
                <p class="text-muted-foreground text-sm mb-2">Quick Actions</p>
                <div class="flex items-center gap-2">
                    <!-- TODO!: BRING THIS BACK LATER -->
                    <Button v-if="!childrenStore.allChildrenSelected" @click="deleteChildren()" variant="outline" size="sm"
                        class=" h-8 border-dashed border-red-500 text-red-500 bg-red-100">
                        <Trash class="mr-2 h-4 w-4 text-red-500" />
                        Delete {{ childrenStore.allChildrenSelected ? childrenData?.count : childrenStore.selectedChildren.length }} children
                    </Button>

                    <Button @click="exportChildren()" variant="outline" size="sm"
                        class=" h-8 border-dashed border-blue-500 text-primary-500 bg-blue-100">
                        <Download class="mr-2 h-4 w-4 text-blue-500" />
                        Export {{ childrenStore.allChildrenSelected ? childrenData?.count  : childrenStore.selectedChildren.length }} children
                    </Button>
                </div>

            </div>

        </template>


        <template #dataTableFacetedFilter>
            <div class="text-right w-full flex flex-col ">
                <p class="text-muted-foreground text-sm mb-2">Filter By</p>
                <div class="flex items-center justify-end gap-2">
                    <ChildGenderFacetedFilter :table="dataTableRef!" @handle-filter="handleGenderFilter"></ChildGenderFacetedFilter>
                    <ChildScreenedFacetedFilter :table="dataTableRef!" @handle-filter="handleScreenedFilter"></ChildScreenedFacetedFilter>

                </div>
            </div>

        </template>
    </DataTable>

    <AlertDialog :open="isAlertDialogOpen" @update:open="isAlertDialogOpen = $event">
        <AlertDialogContent>
            <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete <span class="text-black font-bold">{{
                        childrenStore.selectedChild?.first_name }} {{ childrenStore.selectedChild?.last_name }}</span>
                    and
                    all associated data including screenings. This action is irreversible.
                </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
                <AlertDialogCancel @click="isAlertDialogOpen = false">Cancel</AlertDialogCancel>
                <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    @click="handleDelete" :disabled="deleteStatus === 'pending'">
                    <Loader2 v-if="deleteStatus === 'pending'" class="mr-2 h-4 w-4 animate-spin" />
                    {{ deleteStatus === 'pending' ? 'Deleting...' : `Yes, Delete
                    ${childrenStore.selectedChild?.first_name}'s Data` }}
                </AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>

    <AlertDialog :open="isMultipleDeleteAlertOpen" @update:open="isMultipleDeleteAlertOpen = $event">
        <AlertDialogContent>
            <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete <span class="text-black font-bold">{{
                        childrenStore.selectedChildren.length }} children</span> and all associated data including
                    screenings. This action is irreversible.
                </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
                <AlertDialogCancel @click="isMultipleDeleteAlertOpen = false">Cancel</AlertDialogCancel>
                <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    @click="handleMultipleDelete" :disabled="deleteStatus === 'pending'">
                    <Loader2 v-if="deleteStatus === 'pending'" class="mr-2 h-4 w-4 animate-spin" />
                    {{ deleteStatus === 'pending' ? 'Deleting...' : `Yes, Delete
                    ${childrenStore.selectedChildren.length}
                    Children` }}
                </AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>

</template>
<script setup lang="ts">

import { useChildrenStore, type Child } from '~/stores/children/children.store'
import { type Table, type Row, type ColumnDef } from '@tanstack/vue-table'
import { Badge } from '@/components/ui/badge';
import UserFullAvatar from '@/components/UserFullAvatar.vue'
import { Checkbox } from '@/components/ui/checkbox';
import { ChildScreenedFacetedFilter } from "@/components/child"

import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import {
    MoreVertical,
    Trash,
    Download,
    Eye
} from 'lucide-vue-next'


import { Trash2 } from 'lucide-vue-next'

import { useToast } from '@/components/ui/toast/use-toast'
import type { DataTableFacatedFilterOptions } from '../DataTableFacetedFilter.vue';

const childrenStore = useChildrenStore()
const toast = useToast()

const currentPage = ref(1)
const searchQuery = ref('')
const genderFilter = ref<string>()
const isScreenedFilter = ref<string>()

//SINGLE DELETE ALERT DIALOGUE
const isAlertDialogOpen = ref(false)

const deleteStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle')
const handleDelete = async () => {
    if (!childrenStore.selectedChild?.id) return

    deleteStatus.value = 'pending'
    const { error, status } = await useAsyncData(
        'delete-child',
        () => childrenStore.deleteChild(childrenStore.selectedChild!.id)
    )

    if (error.value) {
        deleteStatus.value = 'error'
        toast.toast({
            title: "Error",
            description: "Failed to delete child",
            variant: "destructive",
        })
    } else {
        deleteStatus.value = 'success'
        toast.toast({
            title: "Success",
            description: `${childrenStore.selectedChild!.first_name}'s data has been deleted successfully`,
        })
        // Emit the deleted child's ID
        emit('on-child-deleted', childrenStore.selectedChild!.id)
        refreshChildren()
    }

    isAlertDialogOpen.value = false
}
//MULTIPLE DELETE ALERT DIALOGUE
const isMultipleDeleteAlertOpen = ref(false)
const handleMultipleDelete = async () => {
    if (!childrenStore.selectedChildren.length) return
    deleteStatus.value = 'pending'
    const { error } = await useAsyncData(
        'delete-children',
        () => childrenStore.deleteMultipleChildren(childrenStore.selectedChildren.map(child => child.id))
    )
    if (error.value) {
        deleteStatus.value = 'error'
        toast.toast({
            title: "Error",
            description: "Failed to delete children",
            variant: "destructive",
        })
    } else {
        deleteStatus.value = 'success'
        toast.toast({
            title: "Success",
            description: `${childrenStore.selectedChildren.length} children have been deleted successfully`,
        })
        // Emit the deleted child's ID
        emit('on-child-deleted', "")
        refreshChildren()

        // Reset the selected rows
        dataTableRef.value?.setRowSelection([] as any)
        childrenStore.resetSelectedChildren()
    }
    isMultipleDeleteAlertOpen.value = false
}


const emit = defineEmits<{
    'on-child-deleted': [childId: string]
}>();


const deleteChildren = () => {
    isMultipleDeleteAlertOpen.value = true
}


// Fetch children list with pagination
const { data: childrenData, status: childrenStatus, refresh: refreshChildren } = useAsyncData(
    'children-list',
    () => childrenStore.fetchChildren(currentPage.value, undefined, {search_query: searchQuery.value, gender: genderFilter.value, screened: isScreenedFilter.value }),
    {
        lazy: true,
        watch: [currentPage]
    }
)

// DATA TABLE DATA
const dataTableRef = ref<Table<Child>>()
const selectedRow = ref<Row<Child>>()
const handleDataTableData = (data: Table<Child>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<Child>) => {
    // Set selected row
    selectedRow.value = row;

    // Update selected screening
    childrenStore.selectedChild = selectedRow.value?.original ?? null;
}
const handlePageIndex = (index: number) => {
    currentPage.value = index
    refreshChildren()
}

const handleNextPage = () => {
    currentPage.value++
    refreshChildren()
}
const handlePreviousPage = () => {
    currentPage.value--
    refreshChildren()
}

const search = (value: string) => {
    searchQuery.value = value

    refreshChildren()
}

const handleGenderFilter = (filteredValues: DataTableFacatedFilterOptions[]) => {
  genderFilter.value = filteredValues.map(filter => filter.value).join(','); // "male,female"
  refreshChildren()
}

const handleScreenedFilter = (filteredValues: DataTableFacatedFilterOptions[]) => {
  isScreenedFilter.value = filteredValues.map(filter => filter.value).join(','); // "male,female"
  refreshChildren()
}

const exportChildren = async () => {
    if(childrenStore.allChildrenSelected){
        childrenStore.exportChildrenStreamCSV()
        return;
    }
    childrenStore.exportToCSV()
}

// end of DATA TABLE DATA
const viewDetails = (childId: string) => {
    navigateTo(`/children/${childId}`)
}

const editChild = (childId: string) => {
    // Implement edit logic
    console.log('Edit child:', childId)
}

const downloadReport = (childId: string) => {
    // Implement download logic
    console.log('Download report:', childId)
}




const deleteChild = (child: Child) => {
    childrenStore.selectChild(child)
    isAlertDialogOpen.value = true
}




// Add this to your columns array
const columns: ColumnDef<Child>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllRowsSelected(),
            'onUpdate:checked': value => {
                table.toggleAllRowsSelected(!!value);
                childrenStore.toggleSelectAll(value);

            },
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => {
                row.toggleSelected(!!value)
                childrenStore.selectChildren(dataTableRef.value?.getFilteredSelectedRowModel().rows.map(row => row.original) ?? []);
            },
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'first_name',
        enableHiding: true,
        header: () => null,
        cell: () => null,
    },
    {

        header: 'Name',
        cell: ({ row }) => h(UserFullAvatar, {
            firstname: row.original.first_name || '',
            middlename: row.original.middle_name,
            lastname: row.original.last_name || '',
            avatarUrl: row.original.avatar_url || '',
        })

    },
    // {
    //     accessorKey: 'middle_name',
    //     header: 'Middle Name',
    //     filterFn: (row, id, value) => {
    //         let fullName = `${row.original.first_name} ${row.original.last_name}`.toLowerCase()
    //         return fullName.includes(value.toLowerCase())
    //     },
    // },
    // {
    //     accessorKey: 'last_name',
    //     header: 'Last Name',
    //     filterFn: (row, id, value) => {
    //         let fullName = `${row.original.first_name} ${row.original.last_name}`.toLowerCase()
    //         return fullName.includes(value.toLowerCase())
    //     },
    // },

    {
        accessorKey: 'gender',
        header: 'Gender',
        filterFn: (row, id, value) => {

            if (value.includes('male') && value.includes('female')) return true;

            if (value == 'male' && row.original.gender.toUpperCase() == 'MALE') return true

            return value == 'female' && row.original.gender.toUpperCase() == 'FEMALE'
        },
    },
    {
        accessorKey: 'country',
        header: 'Country',
        cell: ({ row }) => row.original.country?.name || '',
    },
    {
        accessorKey: 'region',
        header: 'Region',
        cell: ({ row }) => row.original.region?.name || '',
    },
    {
        accessorKey: 'district',
        header: 'District',
        cell: ({ row }) => row.original.district?.name || '',
    },
    {
        accessorKey: 'screened',
        header: 'Screened',
        cell: ({ row }) => row.original.screened ? h(Badge, row.original.screened ? "Yes" : "") : null,
        filterFn: (row, id, value) => {

            if (value.includes('true') && value.includes('false')) return true;

            if (value == 'true' && row.original.screened) return true

            return (value == 'false' && !row.original.screened)
        },
    },
    {
        accessorKey: 'last_screened',
        header: 'Last Screened',
        cell: ({ row }) => row.original.last_screened ? useFormatDateHuman(new Date(row.original.last_screened)) : '',
    },
    {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => h('div', { class: 'flex justify-end' }, [
            h(DropdownMenu, {}, {
                default: () => [
                    h(DropdownMenuTrigger, {}, {
                        default: () => h(Button, {
                            variant: 'ghost',
                            size: 'icon'
                        }, {
                            default: () => h(MoreVertical, { class: 'w-4 h-4' })
                        })
                    }),
                    h(DropdownMenuContent, {}, {
                        default: () => [
                            h(DropdownMenuItem, {
                                onClick: () => viewDetails(row.original.id)
                            }, {
                                default: () => [
                                    h(Eye, { class: 'w-4 h-4 mr-2' }),
                                    'View Details'
                                ]
                            }),
                            // h(DropdownMenuItem, {
                            //     onClick: () => editChild(row.original.id)
                            // }, {
                            //     default: () => [
                            //         h(Edit, { class: 'w-4 h-4 mr-2' }),
                            //         'Edit'
                            //     ]
                            // }),
                            // h(DropdownMenuItem, {
                            //     onClick: () => downloadReport(row.original.id)
                            // }, {
                            //     default: () => [
                            //         h(FileText, { class: 'w-4 h-4 mr-2' }),
                            //         'Download Report'
                            //     ]
                            // }),
                            h(DropdownMenuItem, {
                                onClick: () => deleteChild(row.original),
                                class: 'text-destructive'
                            }, {
                                default: () => [
                                    h(Trash2, { class: 'w-4 h-4 mr-2' }),
                                    'Delete'
                                ]
                            })
                        ]
                    })
                ]
            })
        ])
    }
]

// end of DATA TABLE DATA
</script>
