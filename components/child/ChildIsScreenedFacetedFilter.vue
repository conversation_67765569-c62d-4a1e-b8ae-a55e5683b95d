<template>
    <DataTableFacetedFilter title="Screened" :options="facetedData" @handle-popover-open="handlePopoverOpen"
        @handle-filter="handleFilter">
    </DataTableFacetedFilter>
</template>
<script setup lang="ts">
import { type Table } from '@tanstack/vue-table'
import type { DataTableFacatedFilterOptions } from '../DataTableFacetedFilter.vue';
import { type Child } from '~/stores/children/children.store'


interface TableProps {
    table: Table<Child>
}

const props = defineProps<TableProps>()
const emit = defineEmits(['handleFilter'])

const facetedTypes = [
    {
        label: "Screened",
        value: "true"
    },
    {
        label: "Not Screened",
        value: "false"
    }
]

const handlePopoverOpen = (state: boolean) => {

}

const handleFilter = (filteredValues: DataTableFacatedFilterOptions[]) => {
    emit('handleFilter', filteredValues)
}

const facetedData = computed(() => facetedTypes)


</script>