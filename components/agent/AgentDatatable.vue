<template> 


    <DataTable :columns="columns" :data="data || []" :is-loading="agentStatus == 'pending'" @get-table-data="handleDataTableData" @get-row-data="handleRowClicked">
        <template #dataTableSearch>
            <div class="bg-white rounded-xl p-4 w-1/2">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <Input v-if="dataTableRef" type="search"
                            placeholder="Search agent name or email..."
                            :model-value="(dataTableRef.getColumn('name')?.getFilterValue() as string) ?? ''"
                            @input="dataTableRef.getColumn('name')?.setFilterValue($event.target.value)" />
                    </div>
                </div>
            </div>
        </template>

        <template #dataTableActions v-if="agentStore.selectedAgents.length > 0">
            <div class="ml-5 flex flex-col ">
                <p class="text-muted-foreground text-sm mb-2">Quick Actions</p>
                <div class="flex items-center gap-2">
                    <Button @click="agentStore.exportToCSV()" variant="outline" size="sm"
                        class=" h-8 border-dashed border-blue-500 text-primary-500 bg-blue-100">
                        <Download class="mr-2 h-4 w-4 text-blue-500" />
                        Export {{ agentStore.selectedAgents.length }} agents
                    </Button>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" class="h-8 border-dashed border-purple-500 text-purple-500 bg-purple-100">
                                <Users class="mr-2 h-4 w-4 text-purple-500" />
                                Change Role ({{ agentStore.selectedAgents.length }})
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem
                                v-for="role in agentStore.roles"
                                :key="role.id"
                                @click="handleBulkRoleChange(role.id)"
                                class="cursor-pointer"
                            >
                                {{ role.name }}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </template>
    </DataTable>
</template>

<script setup lang="ts">
import { type Table, type Row, type ColumnDef } from '@tanstack/vue-table'
import { Users, Download } from 'lucide-vue-next'
import { useAgentsStore, type Agent } from '~/stores/agents/agents.store'
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/toast/use-toast'

const agentStore = useAgentsStore()
const { toast } = useToast()

const currentPage = ref(1)
const itemsPerPage = 2500

// Fetch agent list with pagination
const {data, status: agentStatus, refresh: refreshAgents } = useAsyncData(
    'agents-list',
    () => agentStore.fetchAgents(currentPage.value, itemsPerPage),
    {
        lazy: true,
        watch: [currentPage]
    }
)

// Fetch roles if not already loaded
useAsyncData(
    'roles-list',
    () => agentStore.fetchRoles(),
    {
        lazy: true,
        server: false
    }
)

// Handle bulk role change
const handleBulkRoleChange = async (roleId: number) => {
    if (agentStore.selectedAgents.length === 0) {
        toast({
            title: "No agents selected",
            description: "Please select agents to change their roles.",
            variant: "destructive"
        })
        return
    }

    try {
        const agentIds = agentStore.selectedAgents.map(agent => agent.id)
        const roleName = agentStore.roles.find(role => role.id === roleId)?.name || 'Unknown'

        await agentStore.updateMultipleAgentRoles(agentIds, roleId)

        toast({
            title: "Roles updated successfully",
            description: `Updated ${agentIds.length} agent(s) to ${roleName} role.`,
            variant: "default"
        })

        // Reset selection after successful update
        agentStore.resetSelectedAgents()
        if (dataTableRef.value) {
            dataTableRef.value.resetRowSelection()
        }

        // Refresh the agents list
        await refreshAgents()
    } catch (error) {
        toast({
            title: "Failed to update roles",
            description: error instanceof Error ? error.message : "An error occurred while updating roles.",
            variant: "destructive"
        })
    }
}

// Handle single agent role change
const handleSingleRoleChange = async (agentId: string, roleId: number) => {
    try {
        const roleName = agentStore.roles.find(role => role.id === roleId)?.name || 'Unknown'
        const agent = agentStore.agents.find(a => a.id === agentId)
        const agentName = agent ? agentStore.getAgentFullName(agent) : 'Unknown'

        await agentStore.updateAgentRole(agentId, roleId)

        toast({
            title: "Role updated successfully",
            description: `Updated ${agentName}'s role to ${roleName}.`,
            variant: "default"
        })

        // Refresh the agents list
        await refreshAgents()
    } catch (error) {
        toast({
            title: "Failed to update role",
            description: error instanceof Error ? error.message : "An error occurred while updating the role.",
            variant: "destructive"
        })
    }
}

// Get role chip colors based on role name
const getRoleChipClasses = (roleName: string) => {
    const roleColors = {
        'Admin': 'bg-red-100 text-red-800 border-red-200',
        'Supervisor': 'bg-blue-100 text-blue-800 border-blue-200',
        'Manager': 'bg-green-100 text-green-800 border-green-200',
        'Agent': 'bg-purple-100 text-purple-800 border-purple-200',
        'Screener': 'bg-orange-100 text-orange-800 border-orange-200',
        'Coordinator': 'bg-teal-100 text-teal-800 border-teal-200',
        'Assistant': 'bg-pink-100 text-pink-800 border-pink-200',
        'Specialist': 'bg-indigo-100 text-indigo-800 border-indigo-200'
    }

    return roleColors[roleName as keyof typeof roleColors] || 'bg-gray-100 text-gray-800 border-gray-200'
}

// DATA TABLE DATA
const dataTableRef = ref<Table<Agent>>()
const selectedRow = ref<Row<Agent>>()
const handleDataTableData = (data: Table<Agent>) => {
    dataTableRef.value = data
}
const handleRowClicked = async (row: Row<Agent>) => {
    selectedRow.value = row
}

const columns: ColumnDef<Agent>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllRowsSelected(),
            'onUpdate:checked': value => {
                table.toggleAllRowsSelected(!!value);
                agentStore.selectAgents(dataTableRef.value?.getFilteredSelectedRowModel().rows.map(row => row.original) ?? []);
            },
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => {
                row.toggleSelected(!!value)
                agentStore.selectAgents(dataTableRef.value?.getFilteredSelectedRowModel().rows.map(row => row.original) ?? []);
            },
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'name',
        header: 'Agent',
        cell: ({ row }) => h('div', { class: 'flex items-center gap-3' }, [
            h('div', { class: 'w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center' }, [
                h('img', { 
                    src: row.original.avatar_url, 
                    alt: agentStore.getAgentFullName(row.original),
                    class: 'w-10 h-10 rounded-full object-cover',
                    onError: (e: Event) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        ;
                    }
                }),
                h('div', { 
                    class: 'w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 font-medium text-sm',
                    style: 'display: none;'
                }, agentStore.getAgentInitials(row.original))
            ]),
            h('div', {}, [
                h('div', { class: 'font-medium' }, agentStore.getAgentFullName(row.original)),
                h('div', { class: 'text-sm text-gray-500' }, row.original.email)
            ])
        ]),
        filterFn: (row, _id, value) => {
            const fullName = agentStore.getAgentFullName(row.original).toLowerCase()
            const email = row.original.email.toLowerCase()
            const searchValue = value.toLowerCase()
            return fullName.includes(searchValue) || email.includes(searchValue)
        }
    },
    {
        accessorKey: 'role',
        header: 'Role',
        cell: ({ row }) => h('div', { class: 'flex items-center gap-2' }, [
            h('div', {
                class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRoleChipClasses(row.original.role.name)}`
            }, row.original.role.name),
            h(resolveComponent('DropdownMenu'), {}, {
                default: () => [
                    h(resolveComponent('DropdownMenuTrigger'), { asChild: true }, {
                        default: () => h(resolveComponent('Button'), {
                            variant: 'ghost',
                            size: 'sm',
                            class: 'h-6 w-6 p-0 hover:bg-gray-100'
                        }, {
                            default: () => h('svg', {
                                class: 'h-3 w-3',
                                fill: 'none',
                                stroke: 'currentColor',
                                viewBox: '0 0 24 24'
                            }, [
                                h('path', {
                                    'stroke-linecap': 'round',
                                    'stroke-linejoin': 'round',
                                    'stroke-width': '2',
                                    d: 'M19 9l-7 7-7-7'
                                })
                            ])
                        })
                    }),
                    h(resolveComponent('DropdownMenuContent'), { align: 'end' }, {
                        default: () => agentStore.roles.map(role =>
                            h(resolveComponent('DropdownMenuItem'), {
                                key: role.id,
                                class: 'cursor-pointer',
                                onClick: () => handleSingleRoleChange(row.original.id, role.id)
                            }, {
                                default: () => role.name
                            })
                        )
                    })
                ]
            })
        ])
    }
]

// Remove unused emit for now
// const emit = defineEmits<{
//     'on-agent-deleted': [agentId: string]
// }>();
</script>
