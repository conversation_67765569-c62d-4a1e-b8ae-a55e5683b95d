<script setup lang="ts">
import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  type SidebarProps,
} from '@/components/ui/sidebar'
import {
  BookOpen,
  Bot,
  Command,
  Frame,
  LifeBuoy,
  Map,
  PieChart,
  Send,
  Settings2,
  SquareTerminal,
  UsersRound,
    Users,
  School,
  BarChart3,
  UserCheck,
  Building2,
  ClipboardList,
} from 'lucide-vue-next'

import { useUserStore } from '~/stores/auth/user/user.store';

const props = withDefaults(defineProps<SidebarProps>(), {
  variant: 'inset',
})

const userStore = useUserStore();

const data = {
  navMain: [
    
    {
      title: 'Overview',
      url: '/overview',
      icon: BarChart3,
      isActive: true,
    },
    {
      title: 'Children',
      url: '/children',
      icon: Users,
      // items: [
      //   {
      //     title: 'All Children',
      //     url: '/children',
      //   },
      //   {
      //     title: 'Add Child',
      //     url: '/children/add',
      //   },
      //   {
      //     title: 'Reports',
      //     url: '/children/reports',
      //   },
      // ],
    },
    {
      title: 'Schools',
      url: '/schools',
      icon: School,
      // items: [
      //   {
      //     title: 'All Schools',
      //     url: '/schools',
      //   },
      //   {
      //     title: 'Add School',
      //     url: '/schools/add',
      //   },
      //   {
      //     title: 'School Reports',
      //     url: '/schools/reports',
      //   },
      // ],
    },
    {
      title: 'Screenings',
      url: '/screenings',
      icon: ClipboardList,
      // items: [
      //   {
      //     title: 'All Screenings',
      //     url: '/screenings',
      //   },
      //   {
      //     title: 'New Screening',
      //     url: '/screenings/new',
      //   },
      //   {
      //     title: 'Screening Reports',
      //     url: '/screenings/reports',
      //   },
      // ],
    },
    {
      title: 'Agents',
      url: '/agents',
      icon: UsersRound,
      // items: [
      //   {
      //     title: 'All Agents',
      //     url: '/agents',
      //   },
      //   {
      //     title: 'Add Agent',
      //     url: '/agents/add',
      //   },
      //   {
      //     title: 'Agent Activities',
      //     url: '/agents/activities',
      //   },
      // ],
    },
    // {
    //   title: 'Organizations',
    //   url: '/organizations',
    //   icon: Building2,
    //   items: [
    //     {
    //       title: 'All Organizations',
    //       url: '/organizations',
    //     },
    //     {
    //       title: 'Add Organization',
    //       url: '/organizations/add',
    //     },
    //     {
    //       title: 'Organization Reports',
    //       url: '/organizations/reports',
    //     },
    //   ],
    // },
    // {
    //   title: 'Settings',
    //   url: '/settings',
    //   icon: Settings2,
    //   items: [
    //     {
    //       title: 'General',
    //       url: '/settings/general',
    //     },
    //     {
    //       title: 'User Management',
    //       url: '/settings/users',
    //     },
    //     {
    //       title: 'Permissions',
    //       url: '/settings/permissions',
    //     },
    //     {
    //       title: 'System Settings',
    //       url: '/settings/system',
    //     },
    //   ],
    // },
  ],
  navSecondary: [
    {
      title: 'Support',
      url: '#',
      icon: LifeBuoy,
    },
    {
      title: 'Feedback',
      url: '#',
      icon: Send,
    },
  ],
  projects: [
    {
      name: 'Design Engineering',
      url: '#',
      icon: Frame,
    },
    {
      name: 'Sales & Marketing',
      url: '#',
      icon: PieChart,
    },
    {
      name: 'Travel',
      url: '#',
      icon: Map,
    },
  ],
}
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <a href="#">
              <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage :src="'/appIcon.png'" alt="app logo" />
              <AvatarFallback class="rounded-lg">
                CN
              </AvatarFallback>
            </Avatar>
              </div>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">Disability Detect</span>
                <div class="flex items-center gap-1.5">
                  <span class="inline-flex h-2 w-2 rounded-full bg-green-500"></span>
                  <span class="truncate text-xs text-muted-foreground">{{ userStore.currentUser?.organization?.name ?? 'No Organization' }}</span>
                </div>
              </div>
            </a>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
    <SidebarContent>
      <NavMain :items="data.navMain" />
      <!-- <NavProjects :projects="data.projects" />
      <NavSecondary :items="data.navSecondary" class="mt-auto" /> -->
    </SidebarContent>
    <SidebarFooter>
      <NavUser :user="userStore.currentUser as any" />
    </SidebarFooter>
  </Sidebar>
</template>
