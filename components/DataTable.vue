<template>


    <div class="max-w-1/2 my-2 flex justify-between items-center">
        <!-- SEACRCH BAR AREA -->
        <slot name="dataTableSearch"></slot>

        <!-- ACTIONS AREA -->
        <slot name="dataTableActions"></slot>

        <!-- FILTER FACETS AREA -->
        <slot name="dataTableFacetedFilter"></slot>

    </div>
    <div>
        <p class="text-xs text-muted-foreground">Showing <span class="font-bold text-black">{{ Math.min(
            table.getFilteredRowModel().rows.length,
            table.getState().pagination.pageSize
                ) }}</span> out of <span class="font-bold text-black">{{
                    count }}</span> results</p>
    </div>
    <div class=" mx-auto max-w-7xl">
        <div v-if="isLoading">
            <div class="space-y-4 mt-5">
                <Skeleton v-for="i in 5" class="h-10 w-full rounded-xl" />
            </div>
        </div>
        <template v-else>
            <div class="flex flex-col mt-4">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                        <div class="overflow-hidden  md:rounded-xl bg-white">

                            <Table>
                                <TableHeader>
                                    <TableRow v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id">
                                        <TableHead v-for="header in headerGroup.headers" :key="header.id">
                                            <FlexRender v-if="!header.isPlaceholder"
                                                :render="header.column.columnDef.header" :props="header.getContext()" />
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <template v-if="table.getRowModel().rows?.length">
                                        <TableRow class="cursor-pointer"
                                            v-for="(row, index) in table.getRowModel().rows" :key="row.id"
                                            :data-state="row.getIsSelected() ? 'selected' : undefined"
                                            @click="handleSelectedRows(row)">
                                            <TableCell v-for="cell in row.getVisibleCells()" :key="cell.id">
                                                <FlexRender :render="cell.column.columnDef.cell"
                                                    :props="cell.getContext()" />
                                            </TableCell>
                                        </TableRow>
                                    </template>
                                    <template v-else>
                                        <TableRow>
                                            <TableCell :colSpan="columns.length" class="h-24 text-center">
                                                No results.
                                            </TableCell>
                                        </TableRow>
                                    </template>
                                </TableBody>


                            </Table>
                        </div>
                    </div>
                    <div class="m-5 flex justify-center items-center flex-col gap-10">
                        <div class="text-muted-foreground">
                            <p>Page {{ table.getState().pagination.pageIndex + 1 }} of {{ table.getPageCount() }} -
                                {{ count }} results</p>
                        </div>
                        <Pagination v-slot="{ page }" :total="count" :sibling-count="0" show-edges
                            :items-per-page="itemsPerPage" :default-page="1">
                            <PaginationList v-slot="{ items }" class="flex items-center gap-1">
                                <PaginationFirst @click="setPageIndex(1)" />
                                <PaginationPrev :disabled="!canPreviousPage()" @click="previousPage()" />

                                <template v-for="(item, index) in items">
                                    <PaginationListItem v-if="item.type === 'page'" :key="index" :value="item.value"
                                        as-child>
                                        <Button class="w-10 h-10 p-0" @click="setPageIndex(item.value - 1)"
                                            :variant="item.value === props.currentPage ? 'default' : 'outline'">
                                            {{ item.value }}
                                        </Button>
                                    </PaginationListItem>
                                    <PaginationEllipsis v-else :key="item.type" :index="index" />
                                </template>

                                <PaginationNext :disabled="!canNextPage()" @click="nextPage()" />
                                <PaginationLast @click="setPageIndex(Math.round(count / itemsPerPage))" />
                            </PaginationList>
                        </Pagination>
                    </div>
                </div>
            </div>
        </template>

    </div>

</template>
<script setup lang="ts" generic="TData, TValue">
import { getCoreRowModel, useVueTable, FlexRender, getPaginationRowModel, getFilteredRowModel, type ColumnFiltersState, type Updater, getFacetedRowModel, getFacetedUniqueValues, type Row, type ColumnDef } from '@tanstack/vue-table';
import { Skeleton } from '@/components/ui/skeleton'


const emit = defineEmits(['getTableData', 'getRowData', 'setPageIndex', 'nextPage', 'previousPage'])

const props = withDefaults(defineProps<{
    columns: ColumnDef<TData, TValue>[]
    data: TData[]
    isLoading: boolean
    itemsPerPage?: number
    count: number
    currentPage?: number
}>(), {
    itemsPerPage: 20,
    currentPage: 1
})

const columnFilters = ref<ColumnFiltersState>([])
const rowSelection = ref({})

function valueUpdater<T extends Updater<any>>(updaterOrValue: T, ref: Ref) {
    ref.value
        = typeof updaterOrValue === 'function'
            ? updaterOrValue(ref.value)
            : updaterOrValue
}

const table = useVueTable({
    get data() { return props.data },
    get columns() { return props.columns },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    onColumnFiltersChange: updaterOrValue => valueUpdater(updaterOrValue, columnFilters),
    onRowSelectionChange: updaterOrValue => valueUpdater(updaterOrValue, rowSelection),
    state: {
        get columnFilters() {
            return columnFilters.value
        },
        get rowSelection() { return rowSelection.value },
        pagination: {
            pageSize: props.itemsPerPage ?? 20,
            pageIndex: (props.currentPage ?? 1) - 1
        }
    },
    manualPagination: true,
    pageCount: Math.ceil(props.count / (props.itemsPerPage ?? 20))
})

// Emit table data
emit('getTableData', table)

//Go to first page
const setPageIndex = (index: number = 1) => {
    console.log("Setting page index to", index + 1)
    emit('setPageIndex', index + 1)
}

const nextPage = () => {
    emit('nextPage')
}
const previousPage = () => {
    emit('previousPage')
}

const canPreviousPage = () => {
    return props.currentPage > 0
}
const canNextPage = () => {
    return props.currentPage < Math.round(props.count / props.itemsPerPage)
}

// Handles the row data out put when an entry is clicked
const handleSelectedRows = (row: Row<TData>) => {
    emit('getRowData', row)
}


</script>