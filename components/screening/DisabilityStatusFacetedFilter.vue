<template>
    <DataTableFacetedFilter title="Disability Status" :options="facetedData" @handle-filter="handleFilter"
      @handle-popover-open="handlePopoverOpen">
    </DataTableFacetedFilter>
  </template>
  <script setup lang="ts">
  import { type Table } from '@tanstack/vue-table'
  import type { DataTableFacatedFilterOptions } from '../DataTableFacetedFilter.vue';
  import { type ScreeningResult } from '~/stores/screening/screening.store';
  
  interface TableProps {
    table: Table<ScreeningResult>
  }
  
  const props = defineProps<TableProps>()
  
  const columName = "overall_disability_status"
  
  const disabilityStatuses = [
    {
      label: "None detected",
      value: 1
    },
    {
      label: "Potential Single Disability Occurrence",
      value: 2
    },
    {
      label: "Potential Multiple Disability Occurrences",
      value: 3
    },
    {
      label: "Refer For Further Monitoring and Teaching",
      value: 5
    },
    // {
    //   label: "No Action Required",
    //   value: 6
    // },
    // {
    //   label: "Requires Further Monitoring & Teaching",
    //   value: 8
    // },
    // {
    //   label: "Refer for Further Assessment and Require",
    //   value: 9
    // },
    // {
    //   label: "Refer For Further Assessment and Require",
    //   value: 10
    // }
  ]
  
  const handlePopoverOpen = (state: boolean) => {}
  
  const handleFilter = (filteredValues: DataTableFacatedFilterOptions[]) => {
    if (filteredValues.length == 0) return props.table?.getColumn(columName)?.setFilterValue(undefined)
    props.table?.getColumn(columName)?.setFilterValue(filteredValues.map(filterValue => filterValue.value))
  }
  
  const facetedData = computed(() => disabilityStatuses)
  
  </script>