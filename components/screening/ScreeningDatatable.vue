<template>
    <DataTable :count="screeningData?.count ?? 0" :columns="columns" :data="screeningData?.data ?? []" :is-loading="screeningStatus == 'pending'"
        @get-table-data="handleDataTableData" @get-row-data="handleRowClicked">
        <template #dataTableSearch>
            <div class="bg-white rounded-xl p-4 w-1/2">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <Input v-if="dataTableRef" type="search" placeholder="Search..."
                            :model-value="(dataTableRef.getColumn('child_id')?.getFilterValue() as string) ?? ''"
                            @input="dataTableRef.getColumn('child_id')?.setFilterValue($event.target.value)" />
                    </div>
                </div>
            </div>
        </template>

        <template #dataTableActions v-if="screeningStore.selectedScreeningResults.length > 0">
            <div class="ml-5 flex flex-col ">
                <p class="text-muted-foreground text-sm mb-2">Quick Actions</p>
                <div class="flex items-center gap-2">
                    <Button @click="exportScreenings()" variant="outline" size="sm"
                        class=" h-8 border-dashed border-blue-500 text-primary-500 bg-blue-100">
                        <Download class="mr-2 h-4 w-4 text-blue-500" />
                        Export {{ screeningStore.selectedScreeningResults.length }} screenings
                    </Button>
                </div>
            </div>
        </template>

        <template #dataTableFacetedFilter>
            <div class="text-right">
                <p class="text-muted-foreground text-sm mb-2">Filter By</p>
                <div class="flex items-center gap-2">

                    <DisabilityStatusFacetedFilter :table="dataTableRef!">
                    </DisabilityStatusFacetedFilter>

                </div>
            </div>

        </template>
    </DataTable>
</template>

<script setup lang="ts">
import { useScreeningStore, type ScreeningResult } from '~/stores/screening/screening.store'
import { type Table, type Row, type ColumnDef } from '@tanstack/vue-table'
import { Badge } from '@/components/ui/badge';
import UserFullAvatar from '@/components/UserFullAvatar.vue'
import { Checkbox } from '@/components/ui/checkbox';
import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import {
    MoreVertical,
    Download,
    Eye,
    FileText
} from 'lucide-vue-next'
import { DisabilityStatusFacetedFilter } from '~/components/screening'
import { useToast } from '@/components/ui/toast/use-toast'
import { type FilterProps } from '@/utils/type/filterProps'

const props = defineProps<{
    filters?: FilterProps
}>()

const screeningStore = useScreeningStore()
const toast = useToast()

const currentPage = ref(1)

// Fetch screening results with pagination
const { data: screeningData, status: screeningStatus, refresh: refreshScreenings } = useAsyncData(
    'screening-results',
    () => screeningStore.fetchScreeningResults(currentPage.value, undefined , props.filters),
    {
        lazy: true,
        watch: [currentPage]
    }
)

// DATA TABLE DATA
const dataTableRef = ref<Table<ScreeningResult>>()
const selectedRow = ref<Row<ScreeningResult>>()

const handleDataTableData = (data: Table<ScreeningResult>) => dataTableRef.value = data;
const handleRowClicked = async (row: Row<ScreeningResult>) => {
    selectedRow.value = row;
}

const viewDetails = (screeningId: string) => {
    navigateTo(`/screenings/${screeningId}`)
}

const downloadReport = (screeningId: string) => {
    // Implement download logic
    console.log('Download report:', screeningId)
}

const exportScreenings = () => {
    screeningStore.exportToCSV()
}

// Define columns for screening results
const columns: ColumnDef<any>[] = [
    {
        id: 'select',
        header: ({ table }) => h(Checkbox, {
            'checked': table.getIsAllRowsSelected(),
            'onUpdate:checked': value => {
                table.toggleAllRowsSelected(!!value);
                screeningStore.selectScreeningResults(dataTableRef.value?.getFilteredSelectedRowModel().rows.map(row => row.original) ?? []);
            },
            'ariaLabel': 'Select all',
        }),
        cell: ({ row }) => h(Checkbox, {
            'checked': row.getIsSelected(),
            'onUpdate:checked': value => {
                row.toggleSelected(!!value)
                screeningStore.selectScreeningResults(dataTableRef.value?.getFilteredSelectedRowModel().rows.map(row => row.original) ?? []);
            },
            'ariaLabel': 'Select row',
        }),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: 'child_id',
        header: 'Child Name',
        cell: ({ row }) => h(UserFullAvatar, {
            firstname: row.original.child_id.first_name || '',
            lastname: row.original.child_id.last_name || '',
        }),
        filterFn: (row, id, value) => {
            const fullName = `${row.original.child_id.first_name} ${row.original.child_id.last_name}`.toLowerCase()
            return fullName.includes(value.toLowerCase())
        }
    },
    {
        accessorKey: 'overall_disability_status',
        header: 'Disability Status',
        cell: ({ row }) => {
            const status = row.original.overall_disability_status
            let badgeClass = ''
            let text = ''

            if (status === 1) {
                badgeClass = 'bg-green-100 text-green-800'
                text = 'None detected'
            } else if (status === 2) {
                badgeClass = 'bg-red-100 text-red-800'
                text = 'Potential Single Disability Occurrence'
            } else if (status === 3) {
                badgeClass = 'bg-red-100 text-red-800'
                text = 'Potential Multiple Disability Occurrences'
            } else if (status === 5) {
                badgeClass = 'bg-yellow-100 text-yellow-800'
                text = 'Refer For Further Monitoring and Teaching'
            }

            return h(Badge, { class: badgeClass }, () => text)
        },
        filterFn: (row, id, value) => {
            if (value.includes(row.original.overall_disability_status)) {
                return true
            }
            return row.original.overall_disability_status == value
        },

    },
    {
        accessorKey: 'created_at',
        header: 'Screening Date',
        cell: ({ row }) => useFormatDateHuman(new Date(row.original.created_at))
    },
    {
        accessorKey: 'created_by',
        header: 'Screened By',
        cell: ({ row }) => `${row.original.created_by.first_name} ${row.original.created_by.last_name}`
    },
    {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => h('div', { class: 'flex justify-end' }, [
            h(DropdownMenu, {}, {
                default: () => [
                    h(DropdownMenuTrigger, {}, {
                        default: () => h(Button, {
                            variant: 'ghost',
                            size: 'icon'
                        }, {
                            default: () => h(MoreVertical, { class: 'w-4 h-4' })
                        })
                    }),
                    h(DropdownMenuContent, {}, {
                        default: () => [
                            h(DropdownMenuItem, {
                                onClick: () => viewDetails(row.original.id)
                            }, {
                                default: () => [
                                    h(Eye, { class: 'w-4 h-4 mr-2' }),
                                    'View Details'
                                ]
                            }),
                            // h(DropdownMenuItem, {
                            //     onClick: () => downloadReport(row.original.id)
                            // }, {
                            //     default: () => [
                            //         h(FileText, { class: 'w-4 h-4 mr-2' }),
                            //         'Download Report'
                            //     ]
                            // })
                        ]
                    })
                ]
            })
        ])
    }
]
</script>
