<script setup lang="ts">
import { useScreeningStore } from '~/stores/screening/screening.store'

// Default to current week if no dates provided
const props = defineProps<{
    startDate?: string;
    endDate?: string;
}>();

// Get the screening store
const screeningStore = useScreeningStore();

// Fetch data using useAsyncData
const { status, error } = useAsyncData(
    'screening-trend',
    () => screeningStore.fetchScreeningTrend(props.startDate, props.endDate),
    {
        immediate: true,
        lazy: true,
        watch: [() => props.startDate, () => props.endDate]
    }
);

// Compute loading state from status
const isLoading = computed(() => status.value === 'pending' || screeningStore.isTrendLoading);

// Chart configuration
const trendChartOptions = computed(() => ({
    chart: {
        type: 'area',
        toolbar: { show: false },
        sparkline: { enabled: false },
        height: '100%',
        width: '100%',
        animations: {
            enabled: true
        }
    },
    stroke: { curve: 'smooth', width: 2 },
    colors: ['#9333ea'],
    fill: {
        type: 'gradient',
        gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.45,
            opacityTo: 0.05,
            stops: [50, 100]
        }
    },
    xaxis: {
        categories: screeningStore.trendCategories,
        labels: { style: { colors: '#64748b' } }
    },
    yaxis: { labels: { style: { colors: '#64748b' } } }
}));

const trendChartSeries = computed(() => [{
    name: 'Screenings',
    data: screeningStore.trendValues
}]);
</script>
<template>
   <div class="bg-white rounded-xl p-5">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold">Screening Trend</h2>
        </div>
        <div v-if="isLoading" class="flex justify-center items-center h-[350px]">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
        <div v-else-if="error || screeningStore.trendError" class="flex justify-center items-center h-[350px] text-red-500">
            <p>{{ screeningStore.trendError || 'Failed to load screening trend data' }}</p>
        </div>
        <ClientOnly v-else>
            <apexchart height="350" type="area" :options="trendChartOptions" :series="trendChartSeries">
            </apexchart>
        </ClientOnly>
    </div>
</template>