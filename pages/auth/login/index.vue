<template>
    <section class="py-12 bg-white sm:py-16 lg:py-20">
        <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
            <div class="max-w-sm mx-auto">
                <div class="text-center">
                    <img class="w-24 mx-auto" src="/logo.png" alt="" />
                    <h1 class="mt-12 text-3xl font-bold text-gray-900">Welcome Back</h1>
                    <p class="mt-4 text-sm font-medium text-gray-500">Manage and track child disability screenings with comprehensive insights and analytics.</p>
                </div>

                <!-- <div class="mt-12">
                <button
                    type="button"
                    class="inline-flex items-center justify-center w-full px-6 py-3 text-sm font-semibold leading-5 text-gray-600 transition-all duration-200 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 hover:bg-gray-50 hover:text-gray-900"
                >
                    <img class="w-5 h-5 mr-2" src="https://landingfoliocom.imgix.net/store/collection/clarity-dashboard/images/previews/sign-in/1/google-logo.svg" alt="" />
                    Sign in with Google
                </button>
            </div>

            <div class="relative mt-6">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-200"></div>
                </div>

                <div class="relative flex justify-center">
                    <span class="px-2 text-sm text-gray-400 bg-white"> or </span>
                </div>
            </div> -->

            
                    <div class="space-y-4">
                        <div>
                            <label for="" class="text-sm font-bold text-gray-900"> Email </label>
                            <div class="mt-2">
                                <Input type="email" name="" placeholder="Email address" v-model="email"
                                    class="border block w-full px-4 py-3 placeholder-gray-500 border-gray-300 rounded-lg focus:ring-indigo-600 focus:border-indigo-600 sm:text-sm caret-indigo-600" />
                            </div>
                        </div>

                        <div>
                            <div class="flex items-center justify-between">
                                <label for="" class="text-sm font-bold text-gray-900"> Password </label>

                                <NuxtLink to="/auth/forgot-password" title="" class="text-sm font-medium text-indigo-600 hover:text-indigo-700"
                                    > Forgot Password? </NuxtLink>
                            </div>
                            <div class="mt-2 relative">
                                <Input :type="showPassword ? 'text' : 'password'" name="" placeholder="Password (min. 8 character)" v-model="password" 
                                    class="border block w-full px-4 py-3 placeholder-gray-500 border-gray-300 rounded-lg focus:ring-indigo-600 focus:border-indigo-600 sm:text-sm caret-indigo-600" />
                                <button 
                                    type="button" 
                                    class="absolute inset-y-0 right-0 flex items-center px-4"
                                    @click="showPassword = !showPassword"
                                >
                                    <Eye v-if="!showPassword" class="h-4 w-4 text-gray-500" />
                                    <EyeOff v-else class="h-4 w-4 text-gray-500" />
                                </button>
                            </div>
                        </div>

                        <div class="relative flex items-center">
                            <div class="flex items-center h-5">
                                <Input type="checkbox" name="remember-password" id="remember-password"
                                    class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-600" />
                            </div>

                            <div class="ml-3">
                                <label for="remember-password" class="text-sm font-medium text-gray-900"> Remember Me
                                </label>
                            </div>
                        </div>

                        <div>
                            <Button @click="login" :disabled="authStore.isLoggingUserIn || authStore.success_LoginUser"
                            class="inline-flex items-center justify-center w-full px-6 py-3 text-sm font-semibold leading-5 text-white transition-all duration-200  border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:primary hover:primary">
                            <Loader2 v-if="authStore.isLoggingUserIn" class="ml-2 animate-spin" :size="16"></Loader2>
                            <span v-else>Sign In</span>
                        </Button>
                        </div>
                    </div>
        

                <!-- <div class="mt-6 text-center">
                <p class="text-sm font-medium text-gray-900">Don't have an account? <a href="#" title="" class="font-bold hover:underline"> Sign up now </a></p>
            </div> -->
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
import { useAuthStore } from '~/stores/auth/auth.store';
import { Loader2 ,Eye, EyeOff} from 'lucide-vue-next'
import { toast } from '@/components/ui/toast';

definePageMeta({
    middleware: "already-auth"
})

useHead(
    {
        title: 'Sign In'
    }
)


const authStore = useAuthStore()
const email = ref("")
const password = ref("")
const showPassword = ref(false)

const login = async () => {
    await authStore.loginWithEmail({ email : email.value, password : password.value })

    if (authStore.failed_LoginUser){
        toast({
        title: 'Login Failed',
        description: authStore.loginFailure.message,
      });
      return;
    }

    return useRouter().go(0) //Middleware will handle it from here
}

</script>