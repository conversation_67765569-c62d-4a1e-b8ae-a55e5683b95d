<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem class="hidden md:block">
                        <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Agents</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        </template>
        <section class="space-y-6 p-6">
            <!-- Header Section -->
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Agents</h1>
                    <p class="text-muted-foreground">Manage and monitor screening agents</p>
                </div>
                <!-- DISABLED FOR NOW -->
                <div v-if="false" class="flex gap-2">
                    <Button variant="outline" @click="agentsStore.exportToCSV()">
                        <Download class="w-4 h-4 mr-2" />
                        Export
                    </Button>
                    <Button class="bg-purple-600 hover:bg-purple-700" @click="navigateTo('/agents/add')">
                        <UserPlus class="w-4 h-4 mr-2" />
                        Add Agent
                    </Button>
                </div>
            </div>

            <!-- Agent datatable -->
            <AgentDatatable></AgentDatatable>
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { AgentDatatable } from '~/components/agent'
import { useAgentsStore } from '~/stores/agents/agents.store'
import {
    UserPlus, Download
} from 'lucide-vue-next'

const agentsStore = useAgentsStore()
</script>
