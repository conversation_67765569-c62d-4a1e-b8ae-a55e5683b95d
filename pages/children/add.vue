<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem class="hidden md:block">
                        <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbLink href="/children">Children</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Add Child</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        </template>
        <section class="max-w-5xl mx-auto space-y-8 p-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold">Add Child</h1>
                    <p class="text-muted-foreground">Enter the child's details below</p>
                </div>
                <Button 
                    variant="outline" 
                    class="flex items-center gap-2 border-purple-200 hover:bg-purple-50"
                    @click="navigateTo('/children/bulk-add')"
                >
                    <Upload class="w-4 h-4" />
                    <span>Bulk Upload</span>
                </Button>
            </div>
            <div class="bg-white rounded-xl border border-purple-100">
                <div class="p-8">
                    <form @submit="handleSubmit" class="space-y-12">
                        <!-- Personal Information -->
                        <div class="space-y-8">
                            <div class="flex items-center gap-3 pb-4 border-b border-purple-100">
                                <div class="bg-purple-100 p-2 rounded-lg">
                                    <User class="w-5 h-5 text-purple-600" />
                                </div>
                                <h2 class="text-xl font-semibold text-gray-800">Personal Information</h2>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                                <FormField v-slot="{ componentField, errorMessage }" name="first_name">
                                    <FormItem>
                                        <FormLabel for="first_name" class="text-sm font-medium">First Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="componentField"
                                                :class="{'border-red-500': errorMessage}"
                                                placeholder="Enter first name" 
                                                class="h-12 transition-all border-gray-200 focus:border-purple-400 focus:ring-purple-100" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                                        
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="middle_name">
                                    <FormItem>
                                        <FormLabel for="middle_name" class="text-sm font-medium">Middle Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="componentField"
                                                :class="{'border-red-500': errorMessage}"
                                                placeholder="Enter middle name (optional)" 
                                                class="h-11" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                                   
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="last_name">
                                    <FormItem>
                                        <FormLabel for="last_name" class="text-sm font-medium">Last Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="componentField"
                                                :class="{'border-red-500': errorMessage}"
                                                placeholder="Enter last name" 
                                                class="h-11" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                                       
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="date_of_birth">
                                    <FormItem>
                                        <FormLabel for="date_of_birth" class="text-sm font-medium">Date of Birth</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="componentField"
                                                type="date"
                                                :class="{'border-red-500': errorMessage}"
                                                class="h-11" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                               
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="gender">
                                    <FormItem>
                                        <FormLabel for="gender" class="text-sm font-medium">Gender</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select gender" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="Male">Male</SelectItem>
                                                    <SelectItem value="Female">Female</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                       
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="grade_id">
                                    <FormItem>
                                        <FormLabel for="grade" class="text-sm font-medium">Grade</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select grade" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="grade in childStore.grades" 
                                                        :key="grade.id" 
                                                        :value="grade.id.toString()"
                                                    >
                                                        {{ grade.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                     
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="country_id">
                                    <FormItem>
                                        <FormLabel for="country" class="text-sm font-medium">Country</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" @update:modelValue="handleCountryChange">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select country" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="country in countryData" 
                                                        :key="country.id" 
                                                        :value="country.id"
                                                    >
                                                        {{ country.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                   
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="region_id">
                                    <FormItem>
                                        <FormLabel for="region" class="text-sm font-medium">Region</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" :disabled="!form.country_id" @update:modelValue="handleRegionChange">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select region" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="region in orgStore.regions" 
                                                        :key="region.id" 
                                                        :value="region.id"
                                                    >
                                                        {{ region.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                       
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="district_id">
                                    <FormItem>
                                        <FormLabel for="district" class="text-sm font-medium">District</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" :disabled="!form.region_id">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select district" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="district in orgStore.districts" 
                                                        :key="district.id" 
                                                        :value="district.id"
                                                    >
                                                        {{ district.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                   
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="school_id">
                                    <FormItem>
                                        <FormLabel for="school" class="text-sm font-medium">School</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select school" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="school in schools" 
                                                        :key="school.id" 
                                                        :value="school.id"
                                                    >
                                                        {{ school.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                     
                                    </FormItem>
                                </FormField>
                            </div>
                        </div>
                        <!-- Guardian Information -->
                        <div class="space-y-8">
                            <div class="flex items-center gap-3 pb-4 border-b border-purple-100">
                                <div class="bg-purple-100 p-2 rounded-lg">
                                    <Users class="w-5 h-5 text-purple-600" />
                                </div>
                                <h2 class="text-xl font-semibold text-gray-800">Guardian Information</h2>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                                <FormField v-slot="{ componentField, errorMessage }" name="guardian.first_name">
                                    <FormItem>
                                        <FormLabel for="guardian_first_name" class="text-sm font-medium">First Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="componentField"
                                                :class="{'border-red-500': errorMessage}"
                                                placeholder="Enter guardian's first name" 
                                                class="h-11" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                                      
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="guardian.last_name">
                                    <FormItem>
                                        <FormLabel for="guardian_last_name" class="text-sm font-medium">Last Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="componentField"
                                                :class="{'border-red-500': errorMessage}"
                                                placeholder="Enter guardian's last name" 
                                                class="h-11" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                                        
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="guardian.email">
                                    <FormItem>
                                        <FormLabel for="guardian_email" class="text-sm font-medium">Email Address</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="componentField"
                                                type="email"
                                                :class="{'border-red-500': errorMessage}"
                                                placeholder="Enter guardian's email" 
                                                class="h-11" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                                     
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField: countryCodeField, errorMessage: countryCodeError }" name="guardian.phone_number.countryCode">
                                    <FormItem>
                                        <FormLabel for="guardian_phone_country_code" class="text-sm font-medium">Phone Country Code</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="countryCodeField"
                                                :class="{'border-red-500': countryCodeError}"
                                                class="h-11 w-20"
                                                placeholder="233"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                        
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField: phoneField, errorMessage: phoneError }" name="guardian.phone_number.number">
                                    <FormItem>
                                        <FormLabel for="guardian_phone_number" class="text-sm font-medium">Phone Number</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="phoneField"
                                                :class="{'border-red-500': phoneError}"
                                                class="h-11 flex-1"
                                                placeholder="Enter phone number"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                     
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="guardian.notes">
                                    <FormItem>
                                        <FormLabel for="guardian_notes" class="text-sm font-medium">Relationship Notes</FormLabel>
                                        <FormControl>
                                            <Textarea 
                                                v-bind="componentField"
                                                :class="{'border-red-500': errorMessage}"
                                                placeholder="e.g. Father, Mother, Uncle, etc."
                                                class="resize-none h-24 min-h-[96px]"
                                            />
                                        </FormControl>
                                        <FormMessage />
                                        
                                    </FormItem>
                                </FormField>
                            </div>
                        </div>
                        <div class="flex justify-end gap-4 pt-6">
                            <Button 
                                type="submit" 
                                size="lg" 
                                class="px-8 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-lg transition-all"
                                :loading="isSubmitting"
                            >
                                Add Child
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { User, Users, Upload } from 'lucide-vue-next';
import { toast } from '@/components/ui/toast';
import { useSchoolsStore } from '~/stores/schools/schools.store';
import { useChildrenStore, type CreateChildPayload } from '~/stores/children/children.store';
import { useUserStore } from '~/stores/auth/user/user.store';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { useOrganizationStore } from '~/stores/organization/organization.store';
import * as z from 'zod';

const childFormSchema = toTypedSchema(z.object({
    first_name: z.string().min(1, 'First name is required'),
    middle_name: z.string().nullable(),
    last_name: z.string().min(1, 'Last name is required'),
    date_of_birth: z.string().min(1, 'Date of birth is required'),
    gender: z.string().min(1, 'Gender is required'),
    school_id: z.string().min(1, 'School is required'),
    grade_id: z.string().min(1, 'Grade is required'),
    country_id: z.string().min(1, 'Country is required'),
    region_id: z.string().min(1, 'Region is required'),
    district_id: z.string().min(1, 'District is required'),
    guardian: z.object({
        id: z.string(),
        first_name: z.string().min(1, 'Guardian first name is required'),
        last_name: z.string().min(1, 'Guardian last name is required'),
        email: z.string().email('Invalid email address').min(1, 'Email is required'),
        phone_number: z.object({
            countryCode: z.string().min(1, 'Country code is required'),
            number: z.string().min(1, 'Phone number is required'),
        }),
        notes: z.string(),
    }),
}));

const { handleSubmit: validateSubmit, values: form, errors } = useForm({
    validationSchema: childFormSchema,
    initialValues: {
        first_name: '',
        middle_name: null,  // Changed to null to match payload type
        last_name: '',
        date_of_birth: '',
        gender: '',
        school_id: '',
        grade_id: '',
        country_id: '',
        region_id: '',
        district_id: '',
        guardian: {
            id: '',  // Added to match payload type
            first_name: '',
            last_name: '',
            email: '',
            phone_number: {
                countryCode: '233',
                number: '',
            },
            notes: '',
        },
    },
});

const isSubmitting = ref(false);
const userStore = useUserStore();
const schoolsStore = useSchoolsStore();
const childStore = useChildrenStore();
const orgStore = useOrganizationStore();

// Fetch data for dropdowns
const { data: schoolsData } = await useAsyncData(
    'schools-list',
    () => schoolsStore.fetchAllSchools(1, 100),
);

// Fetch data for countries
const { data: countryData, status: countryStatus } = await useAsyncData(
    'countries',
    () => orgStore.fetchCountries(),
);

// Fetch data for grades
const { data: gradesData, error: gradesError } = await useAsyncData(
    'grades',
    () => childStore.fetchGrades(),
);

const schools = computed(() => schoolsData.value?.data || []);

// Handle country change
const handleCountryChange = async (countryId: string) => {
    console.log('Country changed:', countryId);
    // Reset dependent fields
    form.region_id = '';
    form.district_id = '';
    if (!countryId) return;
    try {
        await orgStore.fetchRegions(countryId);
    } catch (error) {
        toast({
            title: 'Error',
            description: 'Failed to load regions',
            variant: 'destructive',
        });
    }
};

// Handle region change
const handleRegionChange = async (regionId: string) => {
    // Reset district
    form.district_id = '';
    if (!regionId) return;
    try {
        await orgStore.fetchDistricts(regionId);
    } catch (error) {
        toast({
            title: 'Error',
            description: 'Failed to load districts',
            variant: 'destructive',
        });
    }
};

const handleSubmit = validateSubmit(async (values) => {
    try {
        isSubmitting.value = true;
        await childStore.createChild({
            ...(form as CreateChildPayload),
            org_id: userStore.currentUser?.organization?.id!,
        });
        toast({
            title: 'Success',
            description: 'Child added successfully.',
        });
        navigateTo('/children');
    } catch (error) {
        toast({
            title: 'Error',
            description: error instanceof Error ? error.message : 'Failed to create child',
            variant: 'destructive',
        });
    } finally {
        isSubmitting.value = false;
    }
});
</script>

<style scoped>
.group:focus-within label {
    color: theme('colors.purple.600');
}
input, select, textarea {
    transition: all 0.2s ease;
}
input:focus, select:focus, textarea:focus {
    border-color: theme('colors.purple.400');
    ring-color: theme('colors.purple.100');
    transform: translateY(-1px);
}
</style>