<template>
  <NuxtLayout name="dashboard">
    <template #bread_crumb>
      <!-- Breadcrumbs -->
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem class="hidden md:block">
            <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator class="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbLink href="/children">Children</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem v-if="child">
            <BreadcrumbPage>{{ child.first_name }} {{ child.middle_name ? child.middle_name + ' ' : '' }}{{ child.last_name }}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </template>
    <section class="space-y-6 p-6 max-w-7xl mx-auto">


      <!-- Loading state -->
      <div v-if="isLoading" class="space-y-4">
        <Skeleton class="h-12 w-[250px]" />
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton class="h-[200px] rounded-lg" />
          <Skeleton class="h-[200px] rounded-lg" />
          <Skeleton class="h-[200px] rounded-lg" />
        </div>
        <Skeleton class="h-[300px] rounded-lg" />
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="flex flex-col items-center justify-center py-12">
        <AlertCircle class="h-12 w-12 text-red-500 mb-4" />
        <h2 class="text-xl font-semibold mb-2">Error Loading Child Profile</h2>
        <p class="text-muted-foreground mb-6">{{ error }}</p>
        <Button @click="fetchChildData" class="bg-purple-600 hover:bg-purple-700 text-white">Try Again</Button>
      </div>

      <!-- Content -->
      <div v-else-if="child" class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-xl p-6  border border-gray-100">
          <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div class="flex items-center gap-4">
              <Avatar class="h-16 w-16 border-2 border-purple-100">
                <AvatarImage :src="child.avatar_url" v-if="child.avatar_url" />
                <AvatarFallback class="text-xl bg-purple-100 text-purple-700">
                  {{ getInitials(child.first_name + ' ' + child.last_name) }}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 class="text-9xl md:text-3xl font-bold text-gray-900">
                  {{ child.first_name }} {{ child.middle_name ? child.middle_name + ' ' : '' }}{{ child.last_name }}
                </h1>
                <p class="text-muted-foreground text-xs flex items-center gap-2 mt-2">
                  {{ childId }}
                </p>
              </div>
            </div>
            <div class="flex gap-2 self-end md:self-auto">
              <Button variant="outline" @click="navigateTo('/children')">
                <ArrowLeft class="mr-2 h-4 w-4" />
                Back to Children
              </Button>
              <!-- <Button class="bg-purple-600 hover:bg-purple-700 text-white">
                <Plus class="mr-2 h-4 w-4" />
                New Screening
              </Button> -->
            </div>
          </div>
        </div>



        <!-- Child Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="bg-white rounded-xl p-5 space-y-3  border border-gray-100">
            <div class="flex items-center gap-2">
              <div class="p-2 bg-purple-100 rounded-lg">
                <User class="w-5 h-5 text-purple-600" />
              </div>
              <span class="text-sm font-medium text-gray-600">Age</span>
            </div>
            <div class="text-lg font-bold">{{ child.age }} years</div>
            <div class="text-xs text-muted-foreground">Birth date: {{ formatDate(child.date_of_birth) || 'Not specified'
              }}
            </div>
          </div>

          <div class="bg-white rounded-xl p-5 space-y-3  border border-gray-100">
            <div class="flex items-center gap-2">
              <div class="p-2 bg-purple-100 rounded-lg">
                <School class="w-5 h-5 text-purple-600" />
              </div>
              <span class="text-sm font-medium text-gray-600">School</span>
            </div>
            <div class="text-lg font-bold truncate">{{ child.school?.name || 'N/A' }}</div>
            <div class="text-xs text-muted-foreground">Grade: {{ child.grade?.name || 'Not specified' }}</div>
          </div>

          <div class="bg-white rounded-xl p-5 space-y-3  border border-gray-100">
            <div class="flex items-center gap-2">
              <div class="p-2 bg-purple-100 rounded-lg">
                <MapPin class="w-5 h-5 text-purple-600" />
              </div>
              <span class="text-sm font-medium text-gray-600">Location</span>
            </div>
            <div class="text-lg font-bold truncate">{{ child.district?.name || 'N/A' }}</div>
            <div class="text-xs text-muted-foreground">Region: {{ child.region?.name || 'Not specified' }}</div>
          </div>

          <div class="bg-white rounded-xl p-5 space-y-3  border border-gray-100">
            <div class="flex items-center gap-2">
              <div class="p-2 bg-purple-100 rounded-lg">
                <ClipboardCheck class="w-5 h-5 text-purple-600" />
              </div>
              <span class="text-sm font-medium text-gray-600">Screening Status</span>
            </div>
            <div>
              <Badge v-if="child.screened" :class="{
        'bg-green-100 text-green-800': screeningResults.length > 0 && screeningResults[0].overall_disability_status === 1,
        'bg-yellow-100 text-yellow-800': screeningResults.length > 0 && screeningResults[0].overall_disability_status === 2,
        'bg-orange-100 text-orange-800': screeningResults.length > 0 && screeningResults[0].overall_disability_status === 3,
        'bg-red-100 text-red-800': screeningResults.length > 0 && screeningResults[0].overall_disability_status === 4,
      }" class="px-2 py-1 text-sm font-medium rounded-full">
                {{ screeningResults.length > 0 ?
        getDisabilityLabel(screeningResults[0].overall_disability_status.toString()) : 'Screened' }}
              </Badge>
              <Badge v-else class="bg-gray-100 text-gray-800 px-2 py-1 text-sm font-medium rounded-full">Not Screened
              </Badge>
            </div>
            <div class="text-xs text-muted-foreground">Last updated: {{ child.last_screened ?
              formatDate(child.last_screened) : 'Never' }}</div>
          </div>
        </div>

        <!-- Screening Information -->
        <div>
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Screenings</h1>

        </div>
        <ScreeningDatatable :filters="{child_id :child.id }"></ScreeningDatatable>


      </div>
    </section>
  </NuxtLayout>

</template>

<script setup lang="ts">
import { useChildrenStore, type Child } from '~/stores/children/children.store'
import { useScreeningStore } from '~/stores/screening/screening.store'
import { ArrowLeft, User, School, UserRound, ClipboardCheck, ClipboardX, FileText, AlertCircle, MapPin, Plus } from 'lucide-vue-next'
import { ScreeningDatatable } from '~/components/screening'

// Get the child ID from the route
const route = useRoute()
const childId = route.params.id as string

// Initialize stores
const childrenStore = useChildrenStore()
const screeningStore = useScreeningStore()

// State
const child = ref<Child | null>(null)
const screeningResults = ref<any[]>([])
const isLoading = ref(true)
const error = ref<string | null>(null)

// Fetch child data
const fetchChildData = async () => {
  isLoading.value = true
  error.value = null

  try {
    // Fetch children list to get the child data
    const response = await childrenStore.fetchChildren(undefined,undefined,{id : childId})

    // Find the child by ID
    const foundChild = response.data[0]

    if (!foundChild) {
      error.value = 'Child not found'
      return
    }

    child.value = foundChild

    // Fetch screening results
    if (child.value.screened) {
      await screeningStore.fetchScreeningResults()
      screeningResults.value = screeningStore.screeningResults.filter(
        result => result.child_id.id === childId
      )
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred while fetching data'
    console.error('Error fetching child data:', err)
  } finally {
    isLoading.value = false
  }
}

// Format date
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Get initials for avatar
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
}

// Get disability label
const getDisabilityLabel = (status: string) => {
  return childrenStore.getDisabilityLabel(status)
}



// Fetch data on component mount
onMounted(() => {
  fetchChildData()
})
</script>