<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem class="hidden md:block">
                        <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Children</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        </template>
        <section class="space-y-6 p-6">
            <!-- Header Section -->
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Children</h1>
                    <p class="text-muted-foreground">Manage and view all registered children</p>
                </div>
                <div class="flex gap-2">
                    <Button variant="outline" @click="exportToCSV">
                        <Download class="w-4 h-4 mr-2" />
                        Export {{ childrenStore.selectedChildren.length > 0 ? `(${childrenStore.selectedChildren.length})` : '' }}
                    </Button>
                    <Popover>
                    <PopoverTrigger as-child>
                        <Button class="bg-purple-600 hover:bg-purple-700 text-white">
                            <UserPlus class="w-5 h-5 mr-2" />
                            Add Child
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-64 p-0 bg-white">
                        <div class="p-2">
                            <Button 
                                variant="ghost" 
                                class="w-full justify-start rounded-md h-12 text-base"
                                @click="navigateTo('/children/add')"
                            >
                                <User class="w-5 h-5 mr-3" />
                                Add Single Child
                            </Button>
                            <Button 
                                variant="ghost" 
                                class="w-full justify-start rounded-md h-12 text-base"
                                @click="navigateTo('/children/bulk-add')"
                            >
                                <Users class="w-5 h-5 mr-3" />
                                Bulk Add Children
                            </Button>   
                        </div>
                    </PopoverContent>
                </Popover>
                </div>
                
               
            </div>

            <!-- Children stats -->
            <StatsCard :isLoading="statStatus == 'pending'" :stats="stats"></StatsCard>

        <!-- Children Table -->
         <ChildDataTable @on-child-deleted="()=>refreshStats()"></ChildDataTable>

            
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
import StatsCard, { type StatItem } from '~/components/general/StatsCard.vue'
import ChildDataTable from '~/components/child/ChildDataTable.vue'
import {
    UserPlus, User,
    Users, Activity, AlertTriangle, CheckCircle, Download
} from 'lucide-vue-next'
import { useChildrenStore } from '~/stores/children/children.store'



const childrenStore = useChildrenStore()


// Fetch children stats
const { data: statsData, status: statStatus, refresh:refreshStats } = useAsyncData(
    'children-stats',
    () => childrenStore.fetchChildrenStats(),
    { lazy: true }
)


const exportToCSV = () => {
    childrenStore.exportToCSV()
}


// Stats data
const stats = ref<StatItem[]>([
    { title: 'Total Children', value: '0', icon: Users },
    { title: 'Screened', value: '0', icon: Activity },
    { title: 'Screening Rate', value: '0%', icon: AlertTriangle },
    { title: 'Disability Cases', value: '0', icon: CheckCircle }
])

watch(statsData, (newStats) => {
    if (newStats) {
        stats.value = [
            {
                title: 'Total Children',
                value: newStats.total_children.toLocaleString(),
                icon: Users
            },
            {
                title: 'Screened',
                value: newStats.total_screened.toLocaleString(),
                icon: Activity
            },
            {
                title: 'Screening Rate',
                value: `${Math.round(newStats.screening_rate)}%`,
                icon: AlertTriangle
            },
            {
                title: 'Disability Cases',
                value: Object.values(newStats.disability_status_count)
                    .reduce((a, b) => a + b, 0).toLocaleString(),
                icon: CheckCircle
            }
        ]
    }
}, { immediate: true })




</script>
