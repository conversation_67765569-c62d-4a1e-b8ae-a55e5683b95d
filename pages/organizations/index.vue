<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem class="hidden md:block">
                        <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Organizations</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        </template>
        <section class="space-y-6 p-6">
            <!-- Header Section -->
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Organizations</h1>
                    <p class="text-muted-foreground">Manage and monitor partner organizations</p>
                </div>
                <div class="flex gap-2">
                    <Button variant="outline" @click="exportOrganizations">
                        <Download class="w-4 h-4 mr-2" />
                        Export
                    </Button>
                    <Button class="bg-purple-600 hover:bg-purple-700" @click="navigateTo('/organizations/add')">
                        <Building2 class="w-4 h-4 mr-2" />
                        Add Organization
                    </Button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div v-for="stat in stats" :key="stat.title" 
                    class="bg-white rounded-xl p-4 space-y-2">
                    <div class="flex items-center gap-2">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <component :is="stat.icon" class="w-5 h-5 text-purple-600" />
                        </div>
                        <span class="text-sm text-gray-600">{{ stat.title }}</span>
                    </div>
                    <div class="text-2xl font-bold">{{ stat.value }}</div>
                </div>
            </div>

            <!-- Organization Analytics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white rounded-xl p-5 md:col-span-2">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold">Screening Activities</h2>
                        <Select v-model="activityPeriod" class="w-32">
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="year">This Year</option>
                        </Select>
                    </div>
                    <ClientOnly>
                        <apexchart 
                            height="200"
                            type="bar"
                            :options="activityChartOptions"
                            :series="activityChartSeries">
                        </apexchart>
                    </ClientOnly>
                </div>
                <div class="bg-white rounded-xl p-5">
                    <h2 class="text-lg font-semibold mb-4">Top Organizations</h2>
                    <div class="space-y-4">
                        <div v-for="org in topOrganizations" :key="org.id" 
                            class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <Building2 class="w-5 h-5 text-purple-600" />
                                </div>
                                <div>
                                    <div class="font-medium">{{ org.name }}</div>
                                    <div class="text-sm text-gray-500">{{ org.screenings }} screenings</div>
                                </div>
                            </div>
                            <Badge>{{ org.successRate }}%</Badge>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="bg-white rounded-xl p-4">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <Input 
                            placeholder="Search organizations..." 
                            class="w-full"
                            v-model="searchQuery"
                        >
                            <template #prefix>
                                <Search class="w-4 h-4 text-gray-400" />
                            </template>
                        </Input>
                    </div>
                    <div class="flex gap-4">
                        <Select v-model="typeFilter" class="w-40">
                            <option value="">All Types</option>
                            <option value="ngo">NGO</option>
                            <option value="government">Government</option>
                            <option value="private">Private</option>
                        </Select>
                        <Select v-model="statusFilter" class="w-40">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="pending">Pending</option>
                        </Select>
                    </div>
                </div>
            </div>

            <!-- Organizations Table -->
            <div class="bg-white rounded-xl overflow-hidden">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Organization</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Total Schools</TableHead>
                            <TableHead>Total Agents</TableHead>
                            <TableHead>Screenings</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="org in filteredOrganizations" :key="org.id">
                            <TableCell>
                                <div class="flex items-center gap-3">
                                    <div class="p-2 bg-purple-100 rounded-lg">
                                        <Building2 class="w-5 h-5 text-purple-600" />
                                    </div>
                                    <div>
                                        <div class="font-medium">{{ org.name }}</div>
                                        <div class="text-sm text-gray-500">{{ org.location }}</div>
                                    </div>
                                </div>
                            </TableCell>
                            <TableCell>{{ org.type }}</TableCell>
                            <TableCell>{{ org.totalSchools }}</TableCell>
                            <TableCell>{{ org.totalAgents }}</TableCell>
                            <TableCell>
                                <div class="flex items-center gap-2">
                                    <div class="w-24 bg-gray-100 rounded-full h-2">
                                        <div 
                                            class="bg-purple-600 h-2 rounded-full" 
                                            :style="{ width: `${org.screeningProgress}%` }"
                                        ></div>
                                    </div>
                                    <span>{{ org.totalScreenings }}</span>
                                </div>
                            </TableCell>
                            <TableCell>
                                <Badge :variant="getStatusVariant(org.status)">
                                    {{ org.status }}
                                </Badge>
                            </TableCell>
                            <TableCell>
                                <DropdownMenu>
                                    <DropdownMenuTrigger>
                                        <Button variant="ghost" size="icon">
                                            <MoreVertical class="w-4 h-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                        <DropdownMenuItem @click="viewOrganization(org.id)">
                                            <Eye class="w-4 h-4 mr-2" />
                                            View Details
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="editOrganization(org.id)">
                                            <Edit class="w-4 h-4 mr-2" />
                                            Edit
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="viewAnalytics(org.id)">
                                            <BarChart class="w-4 h-4 mr-2" />
                                            Analytics
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="manageAccess(org.id)">
                                            <Key class="w-4 h-4 mr-2" />
                                            Manage Access
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>

            <!-- Pagination -->
            <div class="flex items-center justify-between">
                <p class="text-sm text-gray-500">
                    Showing {{ paginationStart }} to {{ paginationEnd }} of {{ totalOrganizations }} organizations
                </p>
                <div class="flex gap-2">
                    <Button 
                        variant="outline" 
                        :disabled="currentPage === 1"
                        @click="prevPage"
                    >
                        Previous
                    </Button>
                    <Button 
                        variant="outline"
                        :disabled="currentPage === totalPages"
                        @click="nextPage"
                    >
                        Next
                    </Button>
                </div>
            </div>
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
    Building2, Search, Eye, Edit, BarChart, Key,
    MoreVertical, Users, Activity, CheckCircle, Building
} from 'lucide-vue-next'

// Stats data
const stats = ref([
    { title: 'Total Organizations', value: '24', icon: Building },
    { title: 'Active Organizations', value: '18', icon: Activity },
    { title: 'Total Schools', value: '156', icon: Building2 },
    { title: 'Total Agents', value: '892', icon: Users },
])

// Activity data
const activityPeriod = ref('month')
const activityChartOptions = {
    chart: {
        type: 'bar',
        toolbar: { show: false }
    },
    plotOptions: {
        bar: {
            horizontal: false,
            columnWidth: '55%',
            borderRadius: 4
        },
    },
    colors: ['#9333ea'],
    xaxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        labels: { style: { colors: '#64748b' } }
    },
    yaxis: { labels: { style: { colors: '#64748b' } } }
}

const activityChartSeries = [{
    name: 'Screenings',
    data: [440, 505, 414, 671, 427, 613]
}]

// Top organizations
const topOrganizations = ref([
    {
        id: 1,
        name: 'Care Foundation',
        screenings: 1256,
        successRate: 98
    },
    {
        id: 2,
        name: 'Hope Center',
        screenings: 892,
        successRate: 95
    },
    {
        id: 3,
        name: 'Child First',
        screenings: 756,
        successRate: 92
    }
])

// Filters
const searchQuery = ref('')
const typeFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const itemsPerPage = 10

// Mock organizations data
const organizations = ref([
    {
        id: 1,
        name: 'Care Foundation',
        location: 'New York, USA',
        type: 'NGO',
        totalSchools: 12,
        totalAgents: 45,
        totalScreenings: 1256,
        screeningProgress: 78,
        status: 'active'
    }
    // Add more organizations
])

// Computed properties
const filteredOrganizations = computed(() => {
    let filtered = [...organizations.value]
    
    if (searchQuery.value) {
        filtered = filtered.filter(org => 
            org.name.toLowerCase().includes(searchQuery.value.toLowerCase())
        )
    }
    
    if (typeFilter.value) {
        filtered = filtered.filter(org => org.type.toLowerCase() === typeFilter.value)
    }
    
    if (statusFilter.value) {
        filtered = filtered.filter(org => org.status.toLowerCase() === statusFilter.value)
    }
    
    return filtered
})

const totalOrganizations = computed(() => filteredOrganizations.value.length)
const totalPages = computed(() => Math.ceil(totalOrganizations.value / itemsPerPage))
const paginationStart = computed(() => (currentPage.value - 1) * itemsPerPage + 1)
const paginationEnd = computed(() => Math.min(currentPage.value * itemsPerPage, totalOrganizations.value))

// Utility functions
const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
        case 'active': return 'success'
        case 'inactive': return 'secondary'
        case 'pending': return 'warning'
        default: return 'default'
    }
}

// Action handlers
const viewOrganization = (id: string) => navigateTo(`/organizations/${id}`)
const editOrganization = (id: string) => navigateTo(`/organizations/${id}/edit`)
const viewAnalytics = (id: string) => navigateTo(`/organizations/${id}/analytics`)
const manageAccess = (id: string) => navigateTo(`/organizations/${id}/access`)
const exportOrganizations = () => {
    // Implement export logic
}

const prevPage = () => {
    if (currentPage.value > 1) currentPage.value--
}

const nextPage = () => {
    if (currentPage.value < totalPages.value) currentPage.value++
}
</script>
