<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem class="hidden md:block">
                        <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbLink href="/schools">School</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Add School</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        </template>
        <section class="max-w-5xl mx-auto space-y-8 p-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold">Add Bulk School</h1>
                    <p class="text-muted-foreground">Download the attached template, fill and upload</p>
                </div>
            </div>
            <!-- Notice section -->
            <!-- <div class="bg-amber-50 border-l-4 border-amber-400 p-4 rounded-r-lg">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <AlertCircleIcon class="h-6 w-6 text-amber-400" />
                    </div>
                    <div class="ml-3 flex-1">
                        <h3 class="text-base font-semibold text-amber-800">Important: Use Correct Values</h3>
                        <div class="mt-3 text-sm text-amber-700">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <p class="font-semibold mb-2">Grade/Class IDs (Required) - Use ID numbers only:</p>
                                    <div class="bg-white/50 p-3 rounded-md border border-amber-200 space-y-1.5">
                                        <div class="grid grid-cols-[auto,1fr] gap-3">
                                            <div class="font-bold text-amber-800 w-16">ID: 1</div>
                                            <div>Nursery/Pre-K</div>
                                            <div class="font-bold text-amber-800 w-16">ID: 2</div>
                                            <div>Kindergarten</div>
                                            <div class="font-bold text-amber-800 w-16">ID: 3</div>
                                            <div>Grade 1</div>
                                            <div class="font-bold text-amber-800 w-16">ID: 4</div>
                                            <div>Grade 2</div>
                                            <div class="font-bold text-amber-800 w-16">ID: 5</div>
                                            <div>Grade 3</div>
                                            <div class="font-bold text-amber-800 w-16">ID: 6</div>
                                            <div>Grade 4</div>
                                            <div class="font-bold text-amber-800 w-16">ID: 7</div>
                                            <div>Grade 5</div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <p class="font-semibold mb-2">Required Format Guidelines:</p>
                                    <div class="bg-white/50 p-3 rounded-md border border-amber-200 space-y-3">
                                        <div>
                                            <span class="font-medium">Gender:</span>
                                            <div class="mt-1 pl-4">
                                                • Enter exactly as <span class="font-bold text-amber-800">'Male'</span> or <span class="font-bold text-amber-800">'Female'</span>
                                                <div class="text-xs text-amber-600 mt-1">Case sensitive. Other values will be rejected.</div>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="font-medium">Grade:</span>
                                            <div class="mt-1 pl-4">
                                                • Enter the ID number only (1-7)
                                                <div class="text-xs text-amber-600 mt-1">Do not use grade names. Use corresponding ID numbers.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="flex items-center gap-4">
                <Button variant="outline" @click="downloadTemplate">
                    <DownloadIcon class="w-4 h-4 mr-2" />
                    Download Template
                </Button>

            </div>
            
            <div class="bg-white rounded-xl border border-purple-100">
                <div class="p-8">
                    <!-- Step 1: File Upload -->
                    <div v-if="currentStep === 1" class="space-y-6">


                        <div class="grid w-full gap-4">
                            <div class="flex flex-col items-center justify-center w-full min-h-[200px] relative border-2 border-dashed rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                                @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave"
                                @drop.prevent="handleDrop" :class="{ 'border-primary': isDragging }"
                                @click="triggerFileInput">
                                <input type="file" ref="fileInput" class="hidden" accept=".csv"
                                    @change="handleFileSelect" />
                                <div class="flex flex-col items-center justify-center pt-5 pb-6" v-if="!selectedFile">
                                    <UploadIcon class="w-8 h-8 mb-4 text-gray-500" />
                                    <p class="mb-2 text-sm text-gray-500">
                                        <span class="font-semibold">Click to upload</span> or drag and drop
                                    </p>
                                    <p class="text-xs text-gray-500">CSV file only</p>
                                </div>
                                <div v-else class="flex items-center gap-2">
                                    <FileIcon class="w-4 h-4" />
                                    <span class="text-sm font-medium">{{ selectedFile.name }}</span>
                                    <Button variant="ghost" size="icon" @click.stop="removeFile">
                                        <XIcon class="w-4 h-4" />
                                    </Button>
                                </div>
                            </div>

                            <div v-if="uploadError" class="text-sm text-destructive">
                                {{ uploadError }}
                            </div>
                        </div>

                        <div class="flex justify-end">

                            <Button :disabled="!selectedFile || isProcessing" @click="detectHeaders">
                                <Loader2Icon v-if="isProcessing" class="w-4 h-4 mr-2 animate-spin" />
                                Continue
                            </Button>
                        </div>
                    </div>

                    <!-- Step 2: Header Mapping -->
                    <div v-if="currentStep === 2" class="space-y-6">
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium">Map CSV Headers</h4>
                                <p class="text-sm text-muted-foreground">
                                    Match your CSV headers with our system fields
                                </p>
                            </div>

                            <div class="grid gap-4">
                                <div v-for="field in requiredFields" :key="field.key"
                                    class="grid grid-cols-2 gap-4 items-center">
                                    <div class="space-y-1">
                                        <Label class="font-medium">{{ field.label }}</Label>
                                        <p class="text-xs text-muted-foreground">
                                            {{ field.required ? 'Required' : 'Optional' }}
                                        </p>
                                    </div>
                                    <div class="space-y-1">
                                        <Select v-model="headerMappings[field.key]">
                                            <SelectTrigger
                                                :class="{ 'border-destructive': showMappingErrors && field.required && !headerMappings[field.key] }">
                                                <SelectValue :placeholder="`Select header for ${field.label}`" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="header in csvHeaders" :key="header" :value="header">
                                                    {{ header }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <p v-if="showMappingErrors && field.required && !headerMappings[field.key]"
                                            class="text-xs text-destructive">
                                            This field is required
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Mapping Status -->
                            <div class="pt-4">
                                <p class="text-sm" :class="isValidMapping ? 'text-green-600' : 'text-destructive'">
                                    {{ mappingStatusMessage }}
                                </p>
                            </div>
                        </div>

                        <div class="flex justify-between">
                            <Button variant="outline" @click="currentStep = 1">Back</Button>
                            <Button :disabled="!isValidMapping || !csvHeaders.length" @click="processFileWithMapping">
                                Continue
                            </Button>
                        </div>
                    </div>

                    <!-- Step 3: Preview and Upload -->
                    <div v-if="currentStep === 3" class="space-y-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium">Preview</h4>
                                <p class="text-sm text-muted-foreground">
                                    {{ validRecords.length }} valid records found
                                </p>
                            </div>
                            <div class="flex items-center gap-2">
                                <Badge variant="secondary">
                                    {{ validRecords.length }} Valid
                                </Badge>
                                <Badge variant="destructive" v-if="invalidRecords.length">
                                    {{ invalidRecords.length }} Invalid
                                </Badge>
                            </div>
                        </div>

                        <form @submit.prevent="handleSubmit" class="space-y-6">
                            <div class="grid grid-cols-3 gap-3">
                                <!-- Country Selection -->
                                <FormField v-slot="{ componentField, errorMessage }" name="country_id">
                                    <FormItem>
                                        <FormLabel for="country" class="text-sm font-medium">Country</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" @update:modelValue="handleCountryChange">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select country" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="country in countryData" :key="country.id"
                                                        :value="country.id">
                                                        {{ country.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>

                                <!-- Region Selection -->
                                <FormField v-slot="{ componentField, errorMessage }" name="region_id">
                                    <FormItem>
                                        <FormLabel for="region" class="text-sm font-medium">Region</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" :disabled="!form.country_id"
                                                @update:modelValue="handleRegionChange">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select region" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="region in orgStore.regions" :key="region.id"
                                                        :value="region.id">
                                                        {{ region.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>

                                <!-- District Selection -->
                                <FormField v-slot="{ componentField, errorMessage }" name="district_id">
                                    <FormItem>
                                        <FormLabel for="district" class="text-sm font-medium">District</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" :disabled="!form.region_id">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select district" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem v-for="district in orgStore.districts"
                                                        :key="district.id" :value="district.id">
                                                        {{ district.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>

                            </div>
                        </form>



                        <!-- Valid Records -->
                        <div class="border rounded-lg">
                            <div class="max-h-[400px] overflow-y-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Name</TableHead>
                                            <TableHead>Type</TableHead>
                                           
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        <TableRow v-for="(record, index) in validRecords" :key="index">
                                          
                                            <TableCell>{{ record.name }}</TableCell>
                                            <TableCell>{{ record.type }}</TableCell>
                                            
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        </div>

                        <!-- Invalid Records -->
                        <div v-if="invalidRecords.length" class="space-y-2">
                            <h4 class="text-sm font-medium text-destructive">Invalid Records</h4>
                            <div class="border border-destructive rounded-lg p-4 space-y-2">
                                <div v-for="(record, index) in invalidRecords" :key="index" class="text-sm">
                                    Row {{ record.row }}: {{ record.errors.join(', ') }}
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between">
                            <Button variant="outline" @click="currentStep = 2">Back</Button>
                            <pre>{{ Object.keys(errors) }}</pre>
                            <Button
                                :disabled="isUploading || validRecords.length === 0 || Object.keys(errors).length > 0"
                                @click="handleSubmit">
                                <Loader2Icon v-if="isUploading" class="w-4 h-4 mr-2 animate-spin" />
                                Upload {{ validRecords.length }} Records
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </NuxtLayout>
</template>


<script setup lang="ts">
import { useSchoolsStore, type CreateSchoolPayload } from '~/stores/schools/schools.store';
import { useOrganizationStore } from '~/stores/organization/organization.store';
import { useUserStore } from '~/stores/auth/user/user.store';
import { toast } from '@/components/ui/toast';
import { useForm } from 'vee-validate';
import { z } from 'zod'
import {
    DownloadIcon,
    UploadIcon,
    FileIcon,
    XIcon,
    Loader2Icon
} from 'lucide-vue-next'


interface ValidRecord {
    name: string;
    type: string;
}

interface InvalidRecord {
    row: number;
    errors: string[];
}

interface RequiredField {
    key: string;
    label: string;
    required: boolean;
}

const orgStore = useOrganizationStore();
const schoolStore = useSchoolsStore()
const userStore = useUserStore()

import { toTypedSchema } from '@vee-validate/zod';

const bulkUploadFormSchema = toTypedSchema(z.object({
    country_id: z.string().min(1, 'Country is required'),
    region_id: z.string().min(1, 'Region is required'),
    district_id: z.string().min(1, 'District is required'),

}));

const { handleSubmit: validateSubmit, values: form, errors } = useForm({
    validationSchema: bulkUploadFormSchema,
    initialValues: {
        country_id: '',
        region_id: '',
        district_id: '',

    }
});

// Fetch data for countries
const { data: countryData, status: countryStatus } = await useAsyncData(
    'countries',
    () => orgStore.fetchCountries(),
);

// Handle country change
const handleCountryChange = async (countryId: string) => {
    // Reset dependent fields
    form.region_id = '';
    form.district_id = '';
    if (!countryId) return;
    try {
        await orgStore.fetchRegions(countryId);
    } catch (error) {
        toast({
            title: 'Error',
            description: 'Failed to load regions',
            variant: 'destructive',
        });
    }
};

// Handle region change
const handleRegionChange = async (regionId: string) => {
    // Reset district and school
    form.district_id = '';
    if (!regionId) return;
    try {
        await orgStore.fetchDistricts(regionId);
    } catch (error) {
        toast({
            title: 'Error',
            description: 'Failed to load districts',
            variant: 'destructive',
        });
    }
};

// Handle bulk submit
const handleSubmit = validateSubmit(async (values) => {
    try {
        await uploadRecords();
    }
    catch (e) {
        toast({
            title: 'Error',
            description: 'Please select all fields',
            variant: 'destructive',
        });
    }
})


// Update uploadRecords to use form values
const uploadRecords = async () => {

    isUploading.value = true;
    try {
        // Map valid records to the payload structure
        const payload: CreateSchoolPayload[] = validRecords.value.map(record => ({
            name:record.name,
            type_id: parseInt(record.type),
            district_id: form.district_id!,
            organization_id: userStore.currentUser?.organization?.id!,
        } as CreateSchoolPayload))

        // Send data to server for processing
        await schoolStore.bulkCreateSchool(payload)

        toast({
            title: 'Schools added successfully',
            description: `${validRecords.value.length} schools have been added`,
        });
        navigateTo('/schools');
 
    } catch (error) {
        toast({
            title: 'Error',
            description: error instanceof Error ? error.message : 'Failed to upload records',
            variant: 'destructive',
        });
    } finally {
        isUploading.value = false;
    }
}

const currentStep = ref<number>(1)
const fileInput = ref<HTMLInputElement | null>(null)
const selectedFile = ref<File | null>(null)
const isDragging = ref<boolean>(false)
const isProcessing = ref<boolean>(false)
const isUploading = ref<boolean>(false)
const uploadError = ref<string>('')
const csvHeaders = ref<string[]>([])
const headerMappings = ref<Record<string, string>>({})
const showMappingErrors = ref<boolean>(false)

const validRecords = ref<ValidRecord[]>([])
const invalidRecords = ref<InvalidRecord[]>([])

// Define required fields and their mappings
const requiredFields: RequiredField[] = [
    { key: 'name', label: 'School Name', required: true },
    { key: 'type', label: 'School Type', required: true },
]

// Schema for CSV validation
const csvSchema = z.object({
    name: z.string().min(1, 'School name is required'),
    type: z.string().min(1, 'School type is required'),

})

const isValidMapping = computed(() => {
    const requiredFieldsMapped = requiredFields
        .filter(field => field.required)
        .every(field => headerMappings.value[field.key])

    // Check for duplicate mappings
    const selectedHeaders = Object.values(headerMappings.value).filter(Boolean)
    const uniqueHeaders = new Set(selectedHeaders)
    const noDuplicates = selectedHeaders.length === uniqueHeaders.size

    return requiredFieldsMapped && noDuplicates
})

const mappingStatusMessage = computed(() => {
    if (!csvHeaders.value.length) {
        return 'No CSV headers detected'
    }

    const unmappedRequired = requiredFields
        .filter(field => field.required && !headerMappings.value[field.key])
        .map(field => field.label)

    if (unmappedRequired.length > 0) {
        return `Required fields not mapped: ${unmappedRequired.join(', ')}`
    }

    const selectedHeaders = Object.values(headerMappings.value).filter(Boolean)
    const uniqueHeaders = new Set(selectedHeaders)
    if (selectedHeaders.length !== uniqueHeaders.size) {
        return 'Duplicate mappings detected'
    }

    return 'All required fields mapped successfully'
})


const triggerFileInput = () => {
    fileInput.value?.click()
}

const handleDragOver = (e: DragEvent) => {
    isDragging.value = true
}

const handleDragLeave = () => {
    isDragging.value = false
}

const handleDrop = (e: DragEvent) => {
    isDragging.value = false
    const files = e.dataTransfer?.files
    if (files && files.length > 0) {
        validateAndSetFile(files[0])
    }
}

const handleFileSelect = (e: Event) => {
    const files = (e.target as HTMLInputElement).files
    if (files && files.length > 0) {
        validateAndSetFile(files[0])
    }
}

const validateAndSetFile = (file: File) => {
    if (file.type !== 'text/csv') {
        uploadError.value = 'Please upload a CSV file'
        return
    }
    selectedFile.value = file
    uploadError.value = ''
}

const removeFile = () => {
    selectedFile.value = null
    if (fileInput.value) {
        fileInput.value.value = ''
    }
}

const detectHeaders = async () => {
    if (!selectedFile.value) return;

    isProcessing.value = true;
    showMappingErrors.value = false;

    try {
        const text = await selectedFile.value.text();
        const rows = text.split('\n'); // Split the file into rows
        if (rows.length === 0) {
            uploadError.value = 'No content found in CSV file';
            return;
        }

        // Extract headers from the first row
        csvHeaders.value = rows[0].split(',').map(header => header.trim());
        headerMappings.value = {};

        if (csvHeaders.value.length === 0) {
            uploadError.value = 'No headers found in CSV file';
            return;
        }

        // Auto-map headers that match exactly or closely
        requiredFields.forEach(field => {
            let matchingHeader = csvHeaders.value.find(
                header => header.toLowerCase() === field.key.toLowerCase()
            );
            if (!matchingHeader) {
                matchingHeader = csvHeaders.value.find(
                    header => header.toLowerCase() === field.label.toLowerCase()
                );
            }
            if (matchingHeader) {
                headerMappings.value[field.key] = matchingHeader;
            }
        });

        isProcessing.value = false;
        currentStep.value = 2;
    } catch (error) {
        isProcessing.value = false;
        uploadError.value = 'Error processing file: ' + (error instanceof Error ? error.message : 'Unknown error');
    }
};


const downloadTemplate = () => {
    const headers = [
        'name',
        'type',
    ];

    // Generate CSV content
    const csvContent = headers.join(',') + '\n';

    // Create a Blob and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'schools_upload_template.csv';
    document.body.appendChild(a);
    a.click();

    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
};



const processFileWithMapping = async () => {
    if (!isValidMapping.value) {
        showMappingErrors.value = true;
        return;
    }
    if (!selectedFile.value) return;

    isProcessing.value = true;
    validRecords.value = [];
    invalidRecords.value = [];

    try {
        const text = await selectedFile.value.text();
        const rows = text.split('\n'); // Split the file into rows
        const headers = rows[0].split(',').map(header => header.trim()); // Extract headers

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            if (!row.trim()) continue; // Skip empty rows

            const values = row.split(',').map(value => value.trim());
            const mappedRow: Record<string, string> = {};

            // Map CSV values to system fields
            Object.entries(headerMappings.value).forEach(([systemField, csvHeader]) => {
                if (csvHeader) {
                    const columnIndex = headers.indexOf(csvHeader);
                    if (columnIndex !== -1) {
                        mappedRow[systemField] = values[columnIndex] || '';
                    }
                }
            });

            // Validate the mapped row
            const result = csvSchema.safeParse(mappedRow);
            if (result.success) {
                validRecords.value.push(result.data);
            } else {
                invalidRecords.value.push({
                    row: i + 1,
                    errors: result.error.issues.map(issue => `${issue.path.join('.')}: ${issue.message}`),
                });
            }
        }

        isProcessing.value = false;
        currentStep.value = 3;
    } catch (error) {
        isProcessing.value = false;
        uploadError.value = 'Error processing file: ' + (error instanceof Error ? error.message : 'Unknown error');
    }
};



</script>


