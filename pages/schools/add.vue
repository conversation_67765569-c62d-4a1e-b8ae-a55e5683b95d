<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem class="hidden md:block">
                        <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbLink href="/children">Schools</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Add School</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        </template>
        <section class="max-w-5xl mx-auto space-y-8 p-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold">Add School</h1>
                    <p class="text-muted-foreground">Enter the school's details below</p>
                </div>
                <Button 
                    variant="outline" 
                    class="flex items-center gap-2 border-purple-200 hover:bg-purple-50"
                    @click="navigateTo('/schools/bulk-add')"
                >
                    <Upload class="w-4 h-4" />
                    <span>Bulk Upload</span>
                </Button>
            </div>
            <div class="bg-white rounded-xl border border-purple-100">
                <div class="p-8">
                    <form @submit="handleSubmit" class="space-y-12">
                        <!-- Personal Information -->
                        <div class="space-y-8">
                            <div class="flex items-center gap-3 pb-4 border-b border-purple-100">
                                <div class="bg-purple-100 p-2 rounded-lg">
                                    <User class="w-5 h-5 text-purple-600" />
                                </div>
                                <h2 class="text-xl font-semibold text-gray-800">School Information</h2>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                                <div class="col-span-2">
                                <FormField v-slot="{ componentField, errorMessage }" name="school_name">
                                    <FormItem>
                                        <FormLabel for="school_name" class="text-sm font-medium">School Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                v-bind="componentField"
                                                :class="{'border-red-500': errorMessage}"
                                                placeholder="Enter school name" 
                                                class="h-12 transition-all border-gray-200 focus:border-purple-400 focus:ring-purple-100" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                                        
                                    </FormItem>
                                </FormField>
                            </div>

                                <FormField  v-slot="{ componentField, errorMessage }" name="school_type">
                                    <FormItem>
                                        <FormLabel for="school_type" class="text-sm font-medium">Type</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select school type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="type in schoolTypes" 
                                                        :key="type.id" 
                                                        :value="type.id"
                                                    >
                                                        {{ type.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                       
                                    </FormItem>
                                </FormField>
                              
                                <FormField v-slot="{ componentField, errorMessage }" name="country_id">
                                    <FormItem>
                                        <FormLabel for="country" class="text-sm font-medium">Country</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" @update:modelValue="handleCountryChange">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select country" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="country in countryData" 
                                                        :key="country.id" 
                                                        :value="country.id"
                                                    >
                                                        {{ country.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                   
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="region_id">
                                    <FormItem>
                                        <FormLabel for="region" class="text-sm font-medium">Region</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" :disabled="!form.country_id" @update:modelValue="handleRegionChange">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select region" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="region in orgStore.regions" 
                                                        :key="region.id" 
                                                        :value="region.id"
                                                    >
                                                        {{ region.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                       
                                    </FormItem>
                                </FormField>
                                <FormField v-slot="{ componentField, errorMessage }" name="district_id">
                                    <FormItem>
                                        <FormLabel for="district" class="text-sm font-medium">District</FormLabel>
                                        <FormControl>
                                            <Select v-bind="componentField" :disabled="!form.region_id">
                                                <SelectTrigger class="h-11">
                                                    <SelectValue placeholder="Select district" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem 
                                                        v-for="district in orgStore.districts" 
                                                        :key="district.id" 
                                                        :value="district.id"
                                                    >
                                                        {{ district.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                        <FormMessage />
                                   
                                    </FormItem>
                                </FormField>
                           
                            </div>
                        </div>
                        <div class="flex justify-end gap-4 pt-6">
                            <Button 
                                type="submit" 
                                size="lg" 
                                class="px-8 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-lg transition-all"
                                :loading="isSubmitting"
                            >
                                Add School
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { User,Upload } from 'lucide-vue-next';
import { toast } from '@/components/ui/toast';
import { useUserStore } from '~/stores/auth/user/user.store';
import { useForm } from 'vee-validate';
import { useOrganizationStore } from '~/stores/organization/organization.store';
import { toTypedSchema } from '@vee-validate/zod';
import * as z from 'zod';
import { useSchoolsStore, type CreateSchoolPayload } from '~/stores/schools/schools.store';

const childFormSchema = toTypedSchema(z.object({
    school_name: z.string().min(1, 'School name is required'),
    school_type: z.number().min(1, 'School type is required'),
    country_id: z.string().min(1, 'Country is required'),
    region_id: z.string().min(1, 'Region is required'),
    district_id: z.string().min(1, 'District is required'),

}));

const { handleSubmit: validateSubmit, values: form, errors } = useForm({
    validationSchema: childFormSchema,
    initialValues: {
        school_name: '',
        country_id: '',
        region_id: '',
        district_id: '',

    },
});

const isSubmitting = ref(false);
const userStore = useUserStore();
const orgStore = useOrganizationStore();
const schoolStore = useSchoolsStore();


// Fetch data for countries
const { data: countryData, status: countryStatus } = await useAsyncData(
    'countries',
    () => orgStore.fetchCountries(),
);

// Fetch data for school types
const { data: schoolTypes, status: schoolTypesStatus } = await useAsyncData(
    'schoolTypes',
    () => orgStore.fetchSchoolTypes(),
);

// Handle country change
const handleCountryChange = async (countryId: string) => {
    // Reset dependent fields
    form.region_id = '';
    form.district_id = '';
    if (!countryId) return;
    try {
        await orgStore.fetchRegions(countryId);
    } catch (error) {
        toast({
            title: 'Error',
            description: 'Failed to load regions',
            variant: 'destructive',
        });
    }
};

// Handle region change
const handleRegionChange = async (regionId: string) => {
    // Reset district
    form.district_id = '';
    if (!regionId) return;
    try {
        await orgStore.fetchDistricts(regionId);
    } catch (error) {
        toast({
            title: 'Error',
            description: 'Failed to load districts',
            variant: 'destructive',
        });
    }
};

const handleSubmit = validateSubmit(async (values) => {
    try {
        isSubmitting.value = true;
        await schoolStore.createSchool({
            name:form.school_name!,
            type_id: form.school_type!,
            district_id: form.district_id!,
            organization_id: userStore.currentUser?.organization?.id!
        });
        toast({
            title: 'Success',
            description: 'School added successfully.',
        });
        navigateTo('/schools');
    } catch (error) {
        toast({
            title: 'Error',
            description: error instanceof Error ? error.message : 'Failed to create school',
            variant: 'destructive',
        });
    } finally {
        isSubmitting.value = false;
    }
});
</script>

<style scoped>
.group:focus-within label {
    color: theme('colors.purple.600');
}
input, select, textarea {
    transition: all 0.2s ease;
}
input:focus, select:focus, textarea:focus {
    border-color: theme('colors.purple.400');
    ring-color: theme('colors.purple.100');
    transform: translateY(-1px);
}
</style>