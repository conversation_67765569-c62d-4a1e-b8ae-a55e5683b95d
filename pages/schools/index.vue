<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem class="hidden md:block">
                        <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Schools</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        </template>
        <section class="space-y-6 p-6">
            <!-- Header Section -->
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Schools</h1>
                    <p class="text-muted-foreground">Manage and view all registered schools</p>
                </div>
                <div class="flex gap-2">
                    <Button variant="outline" @click="schoolsStore.exportToCSV()">
                        <Download class="w-4 h-4 mr-2" />
                        Export
                    </Button>
                    <Popover>
                    <PopoverTrigger as-child>
                        <Button class="bg-purple-600 hover:bg-purple-700 text-white">
                            <Building2 class="w-4 h-4 mr-2" />
                            Add School
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent class="w-64 p-0 bg-white">
                        <div class="p-2">
                            <Button 
                                variant="ghost" 
                                class="w-full justify-start rounded-md h-12 text-base"
                                @click="navigateTo('/schools/add')"
                            >
                                <Building2 class="w-5 h-5 mr-3" />
                                Add Single School
                            </Button>
                            <Button 
                                variant="ghost" 
                                class="w-full justify-start rounded-md h-12 text-base"
                                @click="navigateTo('/schools/bulk-add')"
                            >
                                <Building class="w-5 h-5 mr-3" />
                                Bulk Add Schools
                            </Button>   
                        </div>
                    </PopoverContent>
                </Popover>
                </div>
            </div>

            <!-- Stats Cards -->
            <!-- <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div v-for="stat in stats" :key="stat.title" 
                    class="bg-white rounded-xl p-4 space-y-2">
                    <div class="flex items-center gap-2">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <component :is="stat.icon" class="w-5 h-5 text-purple-600" />
                        </div>
                        <span class="text-sm text-gray-600">{{ stat.title }}</span>
                    </div>
                    <div class="text-2xl font-bold">{{ stat.value }}</div>
                </div>
            </div> -->

            <!-- School datatable -->
            <SchoolDatatable></SchoolDatatable>
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">

import { useSchoolsStore } from '~/stores/schools/schools.store'
import { SchoolDatatable } from '~/components/school'
import { 
    Building2, Building, Search, Eye, Edit, Users, MoreVertical,
    School, Activity, Download, CheckCircle 
} from 'lucide-vue-next'

// Stats data
// const stats = ref([
//     { title: 'Total Schools', value: '0', icon: School },
//     { title: 'Total Students', value: '0', icon: Users },
//     { title: 'Screened Students', value: '0', icon: Activity },
//     { title: 'Average Screening Rate', value: '0%', icon: CheckCircle },
// ])

// Filters
// const searchQuery = ref('')

const schoolsStore = useSchoolsStore()
// const currentPage = ref(1)
// const itemsPerPage = 10

// // Fetch schools with pagination
// const { data: schoolsData, status: schoolsStatus } = useAsyncData(
//     'schools-list',
//     () => schoolsStore.fetchAllSchools(currentPage.value, itemsPerPage),
//     { 
//         lazy: true,
      
//     }
// )

// // Update pagination methods to use store values
// const nextPage = () => {
//     if (currentPage.value < schoolsStore.totalPages) {
//         currentPage.value++
//     }
// }

// const prevPage = () => {
//     if (currentPage.value > 1) {
//         currentPage.value--
//     }
// }

// // Add this export function:
// const exportSchools = () => {
//     const data = schoolsStore.schools.map(school => ({
//         'School ID': school.id,
//         'School Name': school.name,
//         'District': school.district.name,
//         'Type': school.type.name,
//         'Total Students': school.total_children_count,
//         'Screened Students': '0',
//         'Screening Rate': `${((0/school.total_children_count) * 100).toFixed(1)}%`
//     }))

//     const csvContent = [
//         Object.keys(data[0]).join(','),
//         ...data.map((row:any) => Object.values(row).join(','))
//     ].join('\n')

//     const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
//     const link = document.createElement('a')
//     const url = URL.createObjectURL(blob)
    
//     link.setAttribute('href', url)
//     link.setAttribute('download', `schools_export_${new Date().toISOString().split('T')[0]}.csv`)
//     link.style.visibility = 'hidden'
//     document.body.appendChild(link)
//     link.click()
//     document.body.removeChild(link)
// }
</script>
