<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/components/ui/toast/use-toast'
import { Loader2, Plus, Edit, Trash2, Save, AlertTriangle, Search, ChevronRight } from 'lucide-vue-next'
import { usePermissionsStore } from '~/stores/permissions/permissions.store'

// State
const { toast } = useToast()
const permissionsStore = usePermissionsStore()
const isRoleDialogOpen = ref(false)
const selectedRole = ref<IRole | null>(null)
const isDeleteDialogOpen = ref(false)
const isSaving = ref(false)
const searchQuery = ref('')
const hasUnsavedChanges = ref(false)
const originalRolesState = ref<IRole[]>([])
const collapsedGroups = ref<Record<string, boolean>>({})

// Toggle group collapse state
const toggleGroup = (resource: string) => {
  collapsedGroups.value[resource] = !collapsedGroups.value[resource]
}

// Role operations
const openRoleDialog = (role?: IRole) => {
  if (role) {
    // Edit existing role - create a deep copy to avoid modifying the original
    selectedRole.value = JSON.parse(JSON.stringify(role))
  } else {
    // Create new role
    selectedRole.value = {
      id: '',
      name: '',
      description: '',
      permissions: [],
      createdAt: new Date().toISOString(),
    }
  }
  isRoleDialogOpen.value = true
}

const openDeleteDialog = (role: IRole) => {
  selectedRole.value = role
  isDeleteDialogOpen.value = true
}

const saveRole = async () => {
  if (!selectedRole.value) return
  isSaving.value = true
  try {
    const isNewRole = !selectedRole.value.id
    if (isNewRole) {
      await permissionsStore.createRole(selectedRole.value)
    } else {
      await permissionsStore.updateRole(selectedRole.value.id, selectedRole.value)
    }
    toast({
      title: isNewRole ? 'Role created' : 'Role updated',
      description: `The role has been successfully ${isNewRole ? 'created' : 'updated'}.`,
    })
    isRoleDialogOpen.value = false
    saveOriginalState() // Update original state after save
  } catch (err) {
    toast({
      title: 'Save failed',
      description: 'There was an error saving the role.',
      variant: 'destructive',
    })
    console.error('Error saving role:', err)
  } finally {
    isSaving.value = false
  }
}

const deleteRole = async () => {
  if (!selectedRole.value) return
  isSaving.value = true
  try {
    await permissionsStore.deleteRole(selectedRole.value.id)
    toast({
      title: 'Role deleted',
      description: 'The role has been successfully deleted.',
    })
    isDeleteDialogOpen.value = false
    saveOriginalState() // Update original state after delete
  } catch (err) {
    toast({
      title: 'Delete failed',
      description: 'There was an error deleting the role.',
      variant: 'destructive',
    })
    console.error('Error deleting role:', err)
  } finally {
    isSaving.value = false
  }
}

// Toggle permission for a role directly in the table
const togglePermissionForRole = (roleId: number, permissionId: number) => {
  const role = permissionsStore.roles.find((r) => r.id === roleId)
  if (!role) return
  const permissionIndex = role.permissions.findIndex((p) => p.id === permissionId)
  const permission = permissionsStore.permissions.find((p) => p.id === permissionId)
  if (!permission) return
  if (permissionIndex === -1) {
    role.permissions.push(permission)
  } else {
    role.permissions.splice(permissionIndex, 1)
  }
  hasUnsavedChanges.value = true
}

// Check if a permission is assigned to a role
const roleHasPermission = (roleId: number, permissionId: number) => {
  const role = permissionsStore.roles.find((r) => r.id === roleId)
  if (!role) return false
  return role.permissions.some((p) => p.id === permissionId)
}

// Group permissions by resource
const permissionsByResource = computed(() => {
  const grouped: Record<string, IPermission[]> = {}
  permissionsStore.permissions.forEach((permission) => {
    const resource = permission.resource || 'Other'
    if (!grouped[resource]) {
      grouped[resource] = []
    }
    // Filter by search query if present
    if (
      !searchQuery.value ||
      permission.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    ) {
      grouped[resource].push(permission)
    }
  })
  return grouped
})

// Save all changes
const saveAllChanges = async () => {
  isSaving.value = true
  try {
    // Save each role that has changes
    const savePromises = permissionsStore.roles.map((role) =>
      permissionsStore.updateRole(role.id, role)
    )
    await Promise.all(savePromises)
    toast({
      title: 'Changes saved',
      description: 'All permission changes have been successfully saved.',
    })
    hasUnsavedChanges.value = false
    saveOriginalState() // Update original state after save
  } catch (err) {
    toast({
      title: 'Save failed',
      description: 'There was an error saving the changes.',
      variant: 'destructive',
    })
    console.error('Error saving changes:', err)
  } finally {
    isSaving.value = false
  }
}

// Save original state for change detection
const saveOriginalState = () => {
  originalRolesState.value = JSON.parse(JSON.stringify(permissionsStore.roles))
}

// Discard changes
const discardChanges = () => {
  // Reset to original state
  permissionsStore.roles = JSON.parse(JSON.stringify(originalRolesState.value))
  hasUnsavedChanges.value = false
  toast({
    title: 'Changes discarded',
    description: 'All changes have been discarded.',
  })
}

// Fetch roles and permissions with useAsyncData
const { data: rolesData, pending: isLoadingRoles, refresh: refreshRoles, error: rolesError } = useAsyncData(
  'roles',
  async () => {
    await permissionsStore.fetchRoles()
    saveOriginalState() // Save original state after fetch
    return permissionsStore.roles
  },
  {
    server: false,
    lazy: true,
  }
)

const { data: permissionsData, pending: isLoadingPermissions, refresh: refreshPermissions, error: permissionsError } = useAsyncData(
  'permissions',
  () => permissionsStore.fetchPermissions(),
  {
    server: false,
    lazy: true,
  }
)
</script>

<template>
  <div class="container mx-auto p-6">
    <header class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Role & Permission Management</h1>
        <p class="text-muted-foreground">Manage user roles and their associated permissions</p>
      </div>
      <div class="flex gap-2">
        <Button @click="openRoleDialog()" class="flex items-center gap-1">
          <Plus class="h-4 w-4" />
          Add Role
        </Button>
        <Button
          variant="default"
          @click="saveAllChanges"
          :disabled="!hasUnsavedChanges || isSaving"
          class="flex items-center gap-1"
        >
          <Loader2 v-if="isSaving" class="h-4 w-4 animate-spin" />
          <Save v-else class="h-4 w-4" />
          Save Changes
        </Button>
        <Button
          variant="outline"
          @click="discardChanges"
          :disabled="!hasUnsavedChanges"
          class="flex items-center gap-1"
        >
          Discard Changes
        </Button>
      </div>
    </header>

    <!-- Loading State -->
    <div v-if="isLoadingRoles || isLoadingPermissions" class="flex items-center justify-center py-12">
      <Loader2 class="h-8 w-8 animate-spin text-primary" />
      <span class="ml-2 text-muted-foreground">Loading roles and permissions...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="rolesError || permissionsError" class="rounded-lg border border-destructive/50 bg-destructive/10 p-6 text-center">
      <AlertTriangle class="mx-auto h-10 w-10 text-destructive" />
      <h3 class="mt-2 text-lg font-medium">Failed to load data</h3>
      <p class="mt-1 text-sm text-muted-foreground">{{ rolesError || permissionsError }}</p>
      <Button variant="outline" class="mt-4" @click="refreshRoles">Try Again</Button>
    </div>

    <!-- Empty State -->
    <div v-else-if="permissionsStore.roles.length === 0" class="rounded-lg border border-dashed p-12 text-center">
      <h3 class="text-lg font-medium">No roles found</h3>
      <p class="mt-1 text-sm text-muted-foreground">Get started by creating a new role.</p>
      <Button variant="outline" class="mt-4" @click="openRoleDialog()">Create Role</Button>
      <Button variant="outline" class="mt-4 ml-2" @click="()=>{
        refreshRoles();
        refreshPermissions();
      }">Refresh</Button>
    </div>

    <!-- Permissions Matrix -->
    <div v-else class="space-y-6">
      <div class="flex items-center gap-4 mb-4">
        <div class="relative flex-1 max-w-md">
          <Search class="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            v-model="searchQuery"
            placeholder="Search permissions..."
            class="pl-8"
          />
        </div>
      </div>
      <div class="border rounded-lg overflow-auto">
        <div class="min-w-max">
          <!-- Table Header -->
          <div class="grid" style="grid-template-columns: 300px repeat(auto-fill, 150px);">
            <!-- Empty cell for the top-left corner -->
            <div class="bg-muted p-2 font-medium text-center border-b border-r sticky top-0 left-0 z-20">
              Permissions / Roles
            </div>
            <!-- Role headers -->
            <div
              v-for="role in permissionsStore.roles"
              :key="role.id"
              class="bg-muted p-2 font-medium text-center border-b border-r sticky top-0 z-10"
            >
              <div class="flex flex-col items-center">
                <span class="font-bold">{{ role.name }}</span>
                <span class="text-xs text-muted-foreground">{{ role.description }}</span>
              </div>
              <div class="flex justify-center mt-2 gap-1">
                <Button variant="ghost" size="icon" @click="openRoleDialog(role)" class="h-6 w-6">
                  <Edit class="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="icon" @click="openDeleteDialog(role)" class="h-6 w-6">
                  <Trash2 class="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
          <!-- Table Body -->
          <div v-for="(permissions, resource) in permissionsByResource" :key="resource">
            <!-- Resource Header -->
            <div 
              class="grid cursor-pointer transition-colors duration-200 hover:bg-muted/30" 
              style="grid-template-columns: 300px repeat(auto-fill, 150px);"
              @click="toggleGroup(resource)"
            >
              <div class="bg-muted/50 p-2 font-semibold border-b border-r sticky left-0 z-10 flex items-center gap-2">
                <ChevronRight
                  class="h-4 w-4 transition-transform duration-200"
                  :class="{ 'transform rotate-90': !collapsedGroups[resource] }"
                />
                {{ resource }}
                <span class="text-xs text-muted-foreground ml-2">({{ permissions.length }} permissions)</span>
              </div>
              <div
                v-for="role in permissionsStore.roles"
                :key="`${resource}-${role.id}-header`"
                class="bg-muted/50 p-2 border-b border-r"
              ></div>
            </div>
            <!-- Permissions -->
            <div
              v-show="!collapsedGroups[resource]"
              class="transition-all duration-200"
            >
              <div
                v-for="permission in permissions"
                :key="permission.id"
                class="grid hover:bg-muted/20"
                style="grid-template-columns: 300px repeat(auto-fill, 150px);"
              >
                <div class="p-2 border-b border-r sticky left-0 bg-card z-10">
                  <div class="font-medium">{{ permission.name }}</div>
                  <div class="text-xs text-muted-foreground">{{ permission.description }}</div>
                </div>
                <div
                  v-for="role in permissionsStore.roles"
                  :key="`${permission.id}-${role.id}`"
                  class="p-2 border-b border-r flex justify-center items-center"
                >
                  <Checkbox
                    :id="`perm-${permission.id}-${role.id}`"
                    :checked="roleHasPermission(role.id, permission.id)"
                    @update:checked="() => togglePermissionForRole(role.id, permission.id)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Role Dialog -->
    <Dialog v-model:open="isRoleDialogOpen">
      <DialogContent class="sm:max-w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{{ selectedRole?.id ? 'Edit Role' : 'Create Role' }}</DialogTitle>
          <DialogDescription>
            {{ selectedRole?.id ? 'Update role details and permissions.' : 'Create a new role and assign permissions.' }}
          </DialogDescription>
        </DialogHeader>
        <div v-if="selectedRole" class="grid gap-4 py-4 overflow-y-auto pr-2">
          <div class="grid gap-2">
            <label for="role-name" class="text-sm font-medium">Role Name</label>
            <Input
              id="role-name"
              v-model="selectedRole.name"
              placeholder="Enter role name"
            />
          </div>
          <div class="grid gap-2">
            <label for="role-description" class="text-sm font-medium">Description</label>
            <Input
              id="role-description"
              v-model="selectedRole.description"
              placeholder="Enter role description"
            />
          </div>
        </div>
        <DialogFooter class="mt-auto pt-4 border-t">
          <Button variant="outline" @click="isRoleDialogOpen = false">Cancel</Button>
          <Button @click="saveRole" :disabled="isSaving">
            <Loader2 v-if="isSaving" class="mr-2 h-4 w-4 animate-spin" />
            <Save v-else class="mr-2 h-4 w-4" />
            {{ isSaving ? 'Saving...' : 'Save Role' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <Dialog v-model:open="isDeleteDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Role</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this role? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <div v-if="selectedRole" class="py-4">
          <p class="font-medium">{{ selectedRole.name }}</p>
          <p class="text-sm text-muted-foreground">{{ selectedRole.description }}</p>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="isDeleteDialogOpen = false">Cancel</Button>
          <Button variant="destructive" @click="deleteRole" :disabled="isSaving">
            <Loader2 v-if="isSaving" class="mr-2 h-4 w-4 animate-spin" />
            <Trash2 v-else class="mr-2 h-4 w-4" />
            {{ isSaving ? 'Deleting...' : 'Delete Role' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>
