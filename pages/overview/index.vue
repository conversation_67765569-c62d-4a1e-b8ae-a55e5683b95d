<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem class="hidden md:block">
                        <BreadcrumbLink href="#">
                            Dashboard
                        </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator class="hidden md:block" />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Overview</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
        </template>
        <section class="space-y-6 p-6">
            <!-- Greeting Section -->
            <div class="flex flex-col space-y-2">
                <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100">
                    {{ greeting }}, {{ user?.firstName }}
                </h1>
                <p class="text-muted-foreground">Here's what's happening with your organization today.</p>

            </div>
            <!-- Stats Grid -->
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div v-for="stat in stats" :key="stat.title" class="bg-white rounded-xl p-4 space-y-2">
                    <div class="flex items-center gap-2">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <component :is="stat.icon" class="w-5 h-5 text-purple-600" />
                        </div>
                        <span class="text-sm text-gray-600">{{ stat.title }}</span>
                    </div>
                    <div class="text-2xl font-bold">
                        <template v-if="isLoadingStats">
                            <div class="animate-pulse h-8 w-16 bg-gray-200 rounded"></div>
                        </template>
                        <template v-else>
                            {{ stat.value }}
                        </template>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="relative bg-white rounded-xl p-6 overflow-hidden">
                <div class="relative z-10">
                    <h2 class="text-lg font-semibold mb-4">Quick Actions</h2>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        <div v-for="action in quickActions" :key="action.title"
                            class="group relative flex flex-col items-center p-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-1 cursor-pointer bg-gray-50/50 border border-gray-100"
                            @click="action.onClick">
                            <div
                                :class="`p-3 rounded-full ${action.bgColor} mb-3 transition-transform group-hover:scale-110`">
                                <component :is="action.icon" class="w-5 h-5" :class="action.iconColor" />
                            </div>
                            <span class="text-sm font-medium text-gray-700">{{ action.title }}</span>
                            <span class="text-xs text-gray-500">{{ action.description }}</span>
                        </div>
                    </div>
                </div>
                <!-- Decorative Elements -->
                <div
                    class="absolute top-0 right-0 w-64 h-64 bg-purple-100/30 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2">
                </div>
                <div
                    class="absolute bottom-0 left-0 w-48 h-48 bg-blue-100/30 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2">
                </div>
            </div>

            <!-- Daily Screening Card -->
            <div class="bg-purple-600 rounded-xl overflow-hidden">
                <div class="relative p-6 md:p-8">
                    <!-- Background Patterns -->
                    <div class="absolute inset-0 opacity-10">
                        <div class="absolute -right-20 -top-20 w-64 h-64 bg-white rounded-full"></div>
                        <div class="absolute -left-20 -bottom-20 w-64 h-64 bg-white rounded-full"></div>
                    </div>

                    <!-- Content -->
                    <div class="relative z-10 md:flex md:items-center md:justify-between">
                        <!-- Left Section -->
                        <div class="flex items-start space-x-4 mb-8 md:mb-0">
                            <div class="p-3 bg-white/10 backdrop-blur-md rounded-2xl">
                                <Users class="w-8 h-8 text-white" />
                            </div>
                            <div>
                                <h2 class="text-xl font-semibold text-white">Daily Screening</h2>
                                <p class="text-purple-200 text-sm">Track your daily progress</p>
                            </div>
                        </div>

                        <!-- Right Section -->
                        <div class="md:text-right">
                            <div class="flex flex-col items-baseline justify-center md:justify-end space-x-3">
                                <template v-if="dailyCountStatus === 'pending'">
                                    <div class="flex flex-col gap-5">
                                        <div class="animate-pulse h-16 w-24 bg-white/20 rounded"></div>
                                        <div class="animate-pulse h-6 w-32 bg-white/20 rounded"></div>
                                    </div>
                                </template>
                                <template v-else>
                                    <p class="text-6xl font-bold text-white tracking-tight">{{ dailyCount }}</p>
                                    <div class="text-purple-200 text-lg">children screened today</div>
                                </template>
                            </div>

                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="relative z-10 mt-6 md:mt-8 flex space-x-4">
                        <Button class="flex-1 bg-white hover:bg-white/90 text-purple-600 border-0"
                           >
                            <Plus class="w-4 h-4 mr-2" />
                            New Screening
                        </Button>
                        <Button variant="secondary"
                            class="flex-1 bg-purple-500 hover:bg-purple-500/90 text-white border-0"
                            >
                            <ChartBar class="w-4 h-4 mr-2" />
                            View All
                        </Button>
                    </div>
                </div>
            </div>

            <!-- Screening Trend Chart -->
            <!-- <div class="bg-white rounded-xl p-5">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">Children Screened</h2>
                </div>
                <ClientOnly>
                    <apexchart 
                        height="300"
                        :options="chartOptions"
                        :series="chartSeries">
                    </apexchart>
                </ClientOnly>
            </div> -->

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Age Distribution -->
                <!-- <div class="bg-white rounded-xl p-5">
                <h2 class="text-lg font-semibold mb-4">Age Distribution</h2>
                <ClientOnly>
                    <apexchart 
                        type="bar"
                        height="300"
                        :options="ageChartOptions"
                        :series="ageChartSeries">
                    </apexchart>
                </ClientOnly>
            </div> -->


            </div>

          
            <AlertDialog :open="showDialog" @update:open="showDialog = $event">
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Coming Soon!</AlertDialogTitle>
                        <AlertDialogDescription>
                            This feature is currently under development and will be available soon.
                            We're working hard to bring you the best experience possible.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Got it</AlertDialogCancel>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { Users, School, ClipboardCheck, Activity, Heart } from 'lucide-vue-next'
import { ChartBar } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { useUserStore } from '@/stores/auth/user/user.store';

//User
const user = useUserStore().currentUser

//Organization
const organizationStore = useOrganizationStore();

// Greeting logic
const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 18) return 'Good afternoon'
    return 'Good evening'
}
const greeting = ref(getGreeting())

// Fetch organization stats
const { data: statsData, pending: isLoadingStats, refresh: refreshStats, error: statsError } = useAsyncData(
    'organization-stats',
    () => organizationStore.fetchStats(),
    {
        lazy: true,
    }
)

// Fetch daily screening count
const { data: dailyCount, status: dailyCountStatus } = useAsyncData(
    'daily-screenings',
    () => organizationStore.fetchDailyScreeningCount(),
    {
        lazy: true,
    }
)


// Initialize stats with default values
const stats = ref([
    { title: 'Screening Rate', value: '0%', icon: Activity },
    { title: 'Total Children', value: '0', icon: Users },
    { title: 'Total Schools', value: '0', icon: School },
    { title: 'Total Screenings', value: '0', icon: ClipboardCheck }
])

// Watch for stats data changes
watch(() => statsData.value, (newStats) => {
    if (newStats) {
        stats.value = [
            { title: 'Total Children', value: newStats.total_children.toLocaleString(), icon: Users },
            { title: 'Total Schools', value: newStats.total_schools.toLocaleString(), icon: School },
            { title: 'Total Screenings', value: newStats.total_screening_results.toLocaleString(), icon: ClipboardCheck },
            { title: 'Total Referred', value: newStats.total_referred_screenings.toLocaleString(), icon: Heart },
            { title: 'Total Agents', value: newStats.total_agents.toLocaleString(), icon: Users }
        ]
    }
}, { immediate: true })

// Age distribution chart
const ageChartOptions = {
    chart: {
        type: 'bar',
        toolbar: {
            show: false
        }
    },
    plotOptions: {
        bar: {
            borderRadius: 4,
            columnWidth: '40%',
        }
    },
    xaxis: {
        categories: ['0-5', '6-10'],
        labels: {
            style: {
                colors: '#64748b'
            }
        }
    },
    yaxis: {
        labels: {
            style: {
                colors: '#64748b'
            }
        }
    },
    grid: {
        borderColor: '#f1f5f9'
    },
    colors: ['#9333ea'],
    dataLabels: {
        enabled: false
    }
}

  // Dialog state
  const showDialog = ref(false)

// Function to toggle dialog
const toggleDialog = () => {
    showDialog.value = !showDialog.value
}

// Navigation function
const navigateToScreenings = () => {
    navigateTo('/screenings')
}


// Add to imports
import { Plus, UserPlus, GraduationCap, ClipboardList, Users2, Settings } from 'lucide-vue-next'
import { useOrganizationStore } from '~/stores/organization/organization.store';

const quickActions = [
    {
        title: 'New Screening',
        description: 'Start a new screening',
        icon: Plus,
        bgColor: 'bg-emerald-100',
        iconColor: 'text-emerald-600',
        onClick: () => toggleDialog()
    },
    {
        title: 'Add Child',
        description: 'Register new child',
        icon: UserPlus,
        bgColor: 'bg-purple-100',
        iconColor: 'text-purple-600',
        onClick: () => navigateTo('/children/add')
    },
    {
        title: 'Add School',
        description: 'Register new school',
        icon: GraduationCap,
        bgColor: 'bg-blue-100',
        iconColor: 'text-blue-600',
        onClick: () => navigateTo('/schools/add')
    },
    {
        title: 'Screenings',
        description: 'View all screenings',
        icon: ClipboardList,
        bgColor: 'bg-amber-100',
        iconColor: 'text-amber-600',
        onClick: () => navigateTo('/screenings')
    },
    {
        title: 'Agents',
        description: 'Manage agents',
        icon: Users2,
        bgColor: 'bg-rose-100',
        iconColor: 'text-rose-600',
        onClick: () => toggleDialog()
    },
    {
        title: 'Settings',
        description: 'System settings',
        icon: Settings,
        bgColor: 'bg-gray-100',
        iconColor: 'text-gray-600',
        onClick: () => toggleDialog()
    }
]



</script>
