<template>
    <div v-if="!screening.objectId">
        <p>Loading...</p>
    </div>

    <template v-else>

        <div class="mt-5">
            <div class="flex items-center justify-between">
                <p class="text-sm font-bold tracking-widest text-gray-500 uppercase">
                    Screening</p>
                <Button :disabled="screeningStore.isDownloadingScreeningPdf" @click="downloadPdf"
                    v-if="screeningStore.success_ScreeningResponses" variant="outline" size="sm" class="h-8">
                    <template v-if="screeningStore.isDownloadingScreeningPdf">
                        Generating PDF
                        <Loader2 class="ml-2 animate-spin" :size="16"></Loader2>
                    </template>
                    <template v-else>
                        Download <Download class="ml-2" :size="16">
                        </Download>
                    </template>

                </Button>
            </div>


            <div class="flex justify-between items-center  bg-purple-50 rounded-lg mt-5 p-5 ">
                <div class="my-5">

                    <div class="flex gap-2 items-center">
                        <NuxtImg class="object-cover w-12 h-12 rounded-full shrink-0"
                            :src="`https://api.dicebear.com/6.x/initials/svg?seed=${screening.child.firstName}`" alt="" />
                        <div>
                            <p class="text-base font-bold text-gray-900 uppercase">
                                {{
                                    screening.child.firstName
                                }} {{
    screening.child.lastName
}}
                            </p>



                        </div>
                    </div>

                    <div class="mt-5">
                        <p class="text-xs text-gray-500">ID:</p>
                        <p class="text-black font-bold text-sm">{{
                            screening.objectId }}
                        </p>
                    </div>



                </div>



                <div class="text-right">
                    <div class="flex justify-end items-center gap-2 my-5">
                        <NuxtImg class="object-cover w-8 h-8 rounded-full shrink-0"
                            :src="`https://api.dicebear.com/6.x/initials/svg?seed=${screening.screenedBy.firstName} ${screening.screenedBy.lastName}`"
                            alt="" />
                        <div>
                            <p class="text-xs text-gray-500">
                                Screened by:</p>

                            <p>{{
                                screening.screenedBy.firstName
                            }} {{
    screening.screenedBy.lastName
}}</p>
                        </div>

                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Date
                            Screened:</p>
                        <p class="text-xs text-black font-bold">{{
                            useFormatDateHuman(new Date(screening.createdAt))
                        }}</p>
                    </div>


                </div>



            </div>

        </div>


        <template v-if="screeningStore.isLoadingScreeningResponses">
            <div class="p-10 flex justify-center items-center h-40">
                <div class="space-y-2">
                    <Skeleton class="h-4 w-[250px]" />
                    <Skeleton class="h-4 w-[200px]" />
                    <Skeleton class="h-4 w-[250px]" />
                    <Skeleton class="h-4 w-[200px]" />
                </div>
            </div>
        </template>
        <template v-else-if="screeningStore.success_ScreeningResponses">
            <div class="flex flex-col gap-10 mt-5">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-xl font-bold text-gray-900">
                            Disability Status</p>
                        <p class="my-1 text-xs font-medium text-gray-500 flex items-center gap-2">
                            <Info :size="13" /><span>Overall
                                disability status of
                                screening</span>
                        </p>

                    </div>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <span
                                    class="cursor-pointer text-xs font-medium text-green-900 bg-green-100 rounded-full inline-flex items-center px-2.5 py-1">
                                    <svg class="-ml-1 mr-1.5 h-2.5 w-2.5 text-green-500" fill="currentColor"
                                        viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3" />
                                    </svg>
                                    {{
                                        screening.disabilityStatus.shortName
                                    }}
                                </span>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{{
                                    screening.disabilityStatus.meaning
                                }}</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>


                </div>
                <div>
                    <div>
                        <p class="text-xl font-bold text-gray-900">
                            Responses</p>
                        <p class="my-1 text-xs font-medium text-gray-500 flex items-center gap-2">
                            <Info :size="13" /><span>View screening
                                responses</span>
                        </p>

                    </div>
                    <div v-for="  response   in   screeningStore.screeningResponses   " class="my-5 rounded-md border p-5">
                        <Collapsible class=" space-y-2 ">
                            <div>
                                <div class="flex items-center justify-between space-x-4">
                                    <h4 class="text-sm font-semibold">
                                        {{ response.category.name }}
                                    </h4>
                                    <CollapsibleTrigger as-child>
                                        <Button variant="ghost" size="sm" class="w-9 p-0">
                                            <ChevronsUpDown class="h-4 w-4" />
                                            <span class="sr-only">Toggle</span>
                                        </Button>
                                    </CollapsibleTrigger>
                                </div>
                                <p class="text-xs">{{
                                    response.analysis.recommendation
                                }}
                                </p>
                            </div>


                            <CollapsibleContent class="my-4" v-for="  innerResponse   in   response.responses  ">

                                <div class="rounded-md border px-4 py-3 font-mono text-sm">
                                    <div class="mb-2">
                                        <Badge variant="outline">
                                            {{
                                                innerResponse.questionNumber
                                            }}
                                        </Badge>
                                    </div>
                                    {{
                                        innerResponse.question.replaceAll('#CHILD',
                                            screening.child.firstName
                                            ?? "Child")
                                    }}
                                    <div class="mt-2 flex justify-end">
                                        <Badge class="bg-green-400 text-black">
                                            {{
                                                innerResponse.answer }}
                                        </Badge>
                                    </div>

                                </div>

                            </CollapsibleContent>
                        </Collapsible>
                    </div>



                </div>

                <div>
                    <div>
                        <p class="text-xl font-bold text-gray-900">
                            Notes</p>
                        <p class="my-1 text-xs font-medium text-gray-500 flex items-center gap-2">
                            <Info :size="13" /><span>Notes added by
                                the
                                screener</span>
                        </p>
                    </div>

                    <div class="bg-gray-50 rounded-xl min-h-[100px] p-5 my-5">
                        <p v-if="screening.comment">
                            {{
                                screening.comment
                            }}
                        </p>
                        <p v-else class="text-xs text-gray-400">No
                            Notes Available</p>
                    </div>

                </div>

            </div>
        </template>
        <template v-else>
            <div class="p-10 flex justify-center items-center h-40">
                <p class="text-sm text-gray-500">Something went
                    wrong, could not load screening
                    responses
                </p>
            </div>

        </template>
        <div class=" p-5 mb-20 bg-red-100 rounded-lg mt-5">
            <div>
                <p class="text-xl font-bold text-red-500">Danger
                    Zone</p>
                <p class="my-1 text-xs font-medium text-black flex items-center gap-2">
                    <Info :size="13" /><span>Actions taken here are
                        non reversible</span>
                </p>

            </div>
            <div class="mt-5">
                <Button disabled variant="destructive" class="w-full cursor-not-allowed">
                    Delete Screening Data
                </Button>
            </div>

        </div>

    </template>
</template>
<script setup lang="ts">
import { useScreeningStore } from '@/stores/screening/screening.store';
import { IScreening } from '@/server/api/screening/model/screening.model';
import { Loader2, Info, Download, ChevronsUpDown } from 'lucide-vue-next'


const props = defineProps<{
    screening: IScreening
}>()
const screeningStore = useScreeningStore()

const downloadPdf = async () => {
    // Check if selected data is available
    if (!screeningStore.selectedScreening.objectId) return alert("Invalid! Cannot download.")

    await screeningStore.downloadPdf()

    if(screeningStore.failed_DownloadScreeningPDF) return alert("Could not download screeening document.")

}


</script>