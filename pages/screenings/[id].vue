<template>
    <NuxtLayout name="dashboard">
        <template #bread_crumb>
      <!-- Breadcrumbs -->
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem class="hidden md:block">
            <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator class="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbLink href="/screenings">Screening</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem v-if="screeningStatus == 'success'">
            <BreadcrumbLink href="#">{{ screeningData.child_id.first_name }} {{ screeningData.child_id.last_name }}</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem v-if="screeningStatus == 'success'">
            <BreadcrumbPage>{{ screeningData.id }}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </template>
        <section class="container mx-auto py-8 px-4 md:px-6 max-w-7xl">
            <!-- Loading State -->
            <div v-if="screeningStatus == 'pending'" class="space-y-6 p-4">
                <!-- Header Skeleton -->
                <div class="bg-white p-4 rounded-lg  shadow-sm">
                    <div class="flex items-center justify-between">
                        <Skeleton class="h-9 w-28" />
                        <Skeleton class="h-9 w-32" />
                    </div>
                </div>

                <!-- Child Info Skeleton -->
                <div
                    class="bg-gradient-to-r from-purple-50/50 to-indigo-50/50 rounded-xl p-6 border border-purple-100/70">
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
                        <div class="flex gap-4 items-start w-full md:w-auto">
                            <Skeleton class="h-16 w-16 rounded-full" />
                            <div class="space-y-2 flex-1">
                                <Skeleton class="h-6 w-48" />
                                <Skeleton class="h-4 w-32" />
                            </div>
                        </div>
                        <div class="w-full md:w-64 bg-white/80 rounded-lg p-4 border border-purple-100/70">
                            <div class="space-y-4">
                                <div class="flex items-center gap-3">
                                    <Skeleton class="h-10 w-10 rounded-full" />
                                    <div class="space-y-2 flex-1">
                                        <Skeleton class="h-4 w-24" />
                                        <Skeleton class="h-5 w-36" />
                                    </div>
                                </div>
                                <div class="pt-3 border-t border-purple-100/70 space-y-2">
                                    <Skeleton class="h-4 w-28" />
                                    <Skeleton class="h-5 w-40" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Skeleton -->
                <div class="bg-white rounded-xl p-6 ">
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                        <div class="space-y-2">
                            <Skeleton class="h-7 w-44" />
                            <Skeleton class="h-4 w-64" />
                        </div>
                        <Skeleton class="h-10 w-48 rounded-lg" />
                    </div>
                </div>

                <!-- Analysis Results Skeleton -->
                <div class="bg-white rounded-xl p-6 ">
                    <div class="space-y-2 mb-6">
                        <Skeleton class="h-7 w-40" />
                        <Skeleton class="h-4 w-56" />
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class=" rounded-lg p-4 space-y-4">
                            <Skeleton class="h-5 w-36" />
                            <Skeleton class="h-8 w-16" />
                            <Skeleton class="h-4 w-48" />
                        </div>
                        <div class=" rounded-lg p-4 space-y-4">
                            <Skeleton class="h-5 w-36" />
                            <Skeleton class="h-8 w-16" />
                            <Skeleton class="h-4 w-48" />
                        </div>
                        <div class=" rounded-lg p-4 space-y-4">
                            <Skeleton class="h-5 w-36" />
                            <Skeleton class="h-8 w-16" />
                            <Skeleton class="h-4 w-48" />
                        </div>
                    </div>
                </div>
            </div>
            <!-- Error State -->
            <div v-else-if="screeningStatus == 'error'"
                class="p-10 flex justify-center items-center h-60 rounded-lg border border-red-100 bg-red-50">
                <div class="text-center max-w-md">
                    <AlertCircle class="h-12 w-12 text-red-500 mx-auto mb-4" />
                    <p class="text-xl font-medium text-gray-900 mb-2">Something went wrong</p>
                    <p class="text-sm text-gray-600 mb-6">{{ screeningError }}</p>
                    <Button  class="mt-4 px-6" variant="outline">Try Again</Button>
                </div>
            </div>

            <!-- Content when data is loaded -->
            <div v-else-if="screeningStatus == 'success'" class="flex flex-col gap-2">
                <!-- Back button and actions -->
                <div class="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm ">
                    <Button @click="$router.back()" variant="ghost"
                        class="flex items-center gap-2 hover:bg-gray-50 transition-colors">
                        <ArrowLeft class="h-4 w-4" />
                        <span>Back to Screenings</span>
                    </Button>

                    <DropdownMenu v-if="screeningData">
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" class="h-9 px-4">
                                <span>Export</span>
                                <ChevronDown class="ml-2 h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" class="w-48">
                            <DropdownMenuItem @click="screeningStore.exportSelectedChildScreeningToCSV(screeningData)">
                                <FileSpreadsheet class="mr-2 h-4 w-4" />
                                <span>Export to CSV</span>
                            </DropdownMenuItem>
                            <!-- <DropdownMenuItem @click="screeningStore.exportToPdf(screeningData)">
                                <FileText class="mr-2 h-4 w-4" />
                                <span>Export to PDF</span>
                            </DropdownMenuItem> -->
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                <!-- Screening Header -->
                <div
                    class="bg-white flex flex-col md:flex-row justify-between items-start md:items-center bg-gradient-to-r from-purple-50/80 to-indigo-50/80 rounded-xl p-6 border ">
                    <div class="w-full md:w-auto">
                        <div class="flex gap-4 items-start">
                            <Avatar class="h-16 w-16">
                                <AvatarImage :src="`https://api.dicebear.com/9.x/initials/svg?seed=${screeningData.child_id.first_name}_${screeningData.child_id.last_name}`"
                                    :alt="`${screeningData.child_id.first_name} ${screeningData.child_id.last_name}`" />
                                <AvatarFallback class="bg-purple-100 text-purple-900">
                                    {{ 's' }}
                                </AvatarFallback>
                            </Avatar>
                            <div class="min-w-0 flex-1 pt-1">
                                <h1 class="text-xl font-bold text-gray-900 truncate max-w-[300px]">
                                    {{ screeningData.child_id.first_name }} {{ screeningData.child_id.last_name }}
                                </h1>
                                <div class="mt-1.5">
                                    <p class="text-sm text-gray-600 flex items-center gap-1.5">
                                        <span>ID:</span>
                                        <span class="font-medium text-gray-800">{{ screeningData?.child_id.id }}</span>
                                    </p>
                                </div>
                                <Badge v-if="screeningStore.isReferred(screeningData.overall_disability_status)" class="text-red-800 bg-red-100">Referred</Badge>
                            </div>
                            
                        </div>
                    </div>

                    <div
                        class="w-full md:w-auto mt-6 md:mt-0 bg-white/90 backdrop-blur-sm p-4 rounded-lg border border-purple-100/70">
                        <div class="flex flex-row items-start gap-3 mb-3">
                           
                            <div class="min-w-0 flex-1">
                                <p class="text-sm text-gray-500">Screened by:</p>
                                <p class="font-medium text-gray-800 truncate mt-0.5">
                                    {{ screeningData.created_by.first_name }} {{ screeningData.created_by.last_name }}
                                </p>
                            </div>
                        </div>

                        <div class="mt-3 pt-3 border-t border-purple-100/70">
                            <p class="text-sm text-gray-500">Date Screened:</p>
                            <p class="font-medium text-gray-800 mt-0.5">
                                {{ useFormatDateHuman(new Date(screeningData.created_at)) }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Disability Status -->
                <div class="bg-white rounded-xl shadow-sm  p-6">
                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                        <div>
                            <div class="flex items-center gap-2">
                                <h2 class="text-xl font-bold text-gray-900">Disability Status</h2>
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <Info :size="16"
                                                class="text-gray-400 hover:text-gray-600 transition-colors" />
                                        </TooltipTrigger>
                                        <TooltipContent class="p-3 max-w-xs">
                                            <p class="text-sm">Overall disability status based on screening results and
                                                analysis</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                            <p class="mt-2 text-sm text-gray-600">
                                Comprehensive evaluation result
                            </p>
                        </div>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <div
                                        :class="[
                                            screeningStore.getDisabilityStatusClass(screeningData.overall_disability_status),
                                            'py-3 px-5 rounded-lg  transition-all duration-300 relative overflow-hidden'
                                        ]"
                                    >
                                        <!-- Background Pattern -->
                                        <div class="absolute inset-0 opacity-10">
                                            <div class="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,currentColor_1px,transparent_1px)] [background-size:8px_8px]"></div>
                                        </div>
                                        
                                        <div class="flex items-center gap-3 ">
                                          
                                            <!-- Status Text -->
                                            <div class="flex flex-col">
                                                <span class="font-medium">
                                                    {{ screeningStore.getDisabilityStatusText(screeningData.overall_disability_status) }}
                                                </span>
                                                <span class="text-xs opacity-75">
                                                    {{ screeningData.analysis_result.rfaCount + screeningData.analysis_result.rfmCount }} indicators detected
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent class="p-3 max-w-xs">
                                    <p class="text-sm" >{{
                screeningStore.getDisabilityStatusDescription(screeningData.overall_disability_status) }}</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                </div>

                <!-- Analysis Results -->
                <div class="bg-white rounded-xl  p-6">
                    <div class="flex items-center gap-2 mb-6">
                        <div class="bg-indigo-100 p-2 rounded-lg">
                            <ChartBar class="h-5 w-5 text-indigo-600" />
                        </div>
                        <div>
                            <p class="text-xl font-bold text-gray-900">Analysis Results</p>
                            <p class="text-sm text-gray-500">Detailed analysis of screening results</p>
                        </div>
                    </div>

                 
                       

                    <!-- Category Results -->
                    <div class="mt-8 bg-gray-50 rounded-xl p-6 ">
                        <p class="text-lg font-semibold mb-4 flex items-center gap-2">
                            <ListChecks class="h-5 w-5 text-gray-700" />
                            Category Results
                        </p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div v-for="(value, key) in screeningData.analysis_result.categoryResults" :key="key"
                                class="flex items-center justify-between p-4 bg-white rounded-lg  hover:bg-gray-50 transition-all duration-300">
                                <span class="font-medium text-gray-800">{{ key }}</span>
                                
                                <Badge :class="screeningStore.getDisabilityAnalysisResultClass(value) ">
                                    {{ screeningStore.getDisabilityAnalysisResultDescription(value) }}
                                </Badge>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Survey Responses -->
                <div class="bg-white rounded-xl  p-6">
                    <div class="flex items-center gap-2 mb-6">
                        <div class="bg-emerald-100 p-2 rounded-lg">
                            <ClipboardCheck class="h-5 w-5 text-emerald-600" />
                        </div>
                        <div>
                            <p class="text-xl font-bold text-gray-900">Responses</p>
                            <p class="text-sm text-gray-500">View screening responses</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div v-for="response in screeningData.survey_responses" :key="response.category.id"
                            class="rounded-lg  overflow-hidden bg-gray-50">
                            <Collapsible class="space-y-2">
                                <div class="p-4 bg-white border-b border-gray-100">
                                    <div class="flex items-center justify-between space-x-4">
                                        <h4 class="text-base font-semibold text-gray-800">{{
                screeningStore.getCategoryLongName(response.category.l10nKey)
            }}</h4>
                                        <CollapsibleTrigger as-child>
                                            <Button variant="ghost" size="sm"
                                                class="w-9 p-0 hover:bg-gray-100 rounded-full">
                                                <ChevronsUpDown class="h-4 w-4" />
                                                <span class="sr-only">Toggle</span>
                                            </Button>
                                        </CollapsibleTrigger>
                                    </div>
                                    <!-- <p class="text-sm text-gray-600 mt-1">{{'Recommendation'}}</p> -->
                                </div>

                                <CollapsibleContent class="space-y-4 p-4">
                                    <div v-for="innerResponse in response.responses" :key="innerResponse.question.id"
                                        class="rounded-lg border border-gray-200 px-5 py-4 text-sm bg-white hover:bg-gray-50 transition-all duration-300">
                                        <div class="mb-3">
                                            <Badge variant="outline"
                                                class="bg-gray-50 border-gray-200 text-gray-700 font-medium">
                                                {{ screeningStore.getQuestionMeta(innerResponse.question.l10nKey)?.cfId.toUpperCase() }}
                                            </Badge>
                                            <Badge variant="outline"
                                                class="bg-gray-50 border-gray-200 text-gray-700 font-medium">
                                                Question {{ screeningStore.getQuestionMeta(innerResponse.question.l10nKey)?.questionNumber }}
                                            </Badge>
                                        </div>
                                        <p class="text-gray-800">{{getQuestion( innerResponse.question.l10nKey, {child :  screeningData.child_id.first_name}) }}</p>
                                        <div class="mt-3 flex justify-end">
                                            <Badge class="bg-emerald-100 text-emerald-800 px-3 py-1">
                                                {{ screeningStore.getAnswerDisplayValue(innerResponse.answer.l10nKey)  }}
                                            </Badge>
                                        </div>
                                    </div>
                                </CollapsibleContent>
                            </Collapsible>
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="bg-white rounded-xl  p-6 mb-10">
                    <div class="flex items-center gap-2 mb-4">
                        <div class="bg-blue-100 p-2 rounded-lg">
                            <FileText class="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                            <p class="text-xl font-bold text-gray-900">Notes</p>
                            <p class="text-sm text-gray-500">Notes added by the screener</p>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-xl min-h-[120px] p-6 ">
                        <p v-if="screeningData.comment" class="whitespace-pre-line text-gray-800">{{ screeningData.comment }}
                        </p>
                        <div v-else class="flex items-center justify-center h-[80px] text-gray-400">
                            <div class="flex items-center gap-2">
                                <NotepadText class="h-5 w-5" />
                                <p>No Notes Available</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
  
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { useScreeningStore } from '~/stores/screening/screening.store'
import {
    ArrowLeft,
    Info,
    Download,
    ChevronsUpDown,
    Loader2,
    AlertCircle,
    ChartBar,
    ListChecks,
    ClipboardCheck,
    FileText,
    NotepadText,
    ChevronDown, FileSpreadsheet,
} from 'lucide-vue-next'
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '@/components/ui/tooltip'
import {
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger
} from '@/components/ui/collapsible'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { getQuestion } from '@/utils/questions'

// Get the screening ID from the route
const route = useRoute()
const screeningId = route.params.id as string

// Initialize store
const screeningStore = useScreeningStore()


// Fetch screening data
const { data: screeningData, status: screeningStatus, error: screeningError, refresh: refreshScreening } = useAsyncData(
    'screening-details',
    async () => {
        const response = await screeningStore.fetchScreeningResults(undefined,undefined,{id : screeningId})
        const foundScreening = response.data[0]
        if (!foundScreening) {
            throw new Error('Screening not found')
        }
        return foundScreening
    },
    {
        watch: [screeningId]
    }
)



</script>
