<script setup lang="ts">
import { useScreeningStore } from '~/stores/screening/screening.store'
import type { Question, QuestionItem, ScreeningQuestion } from '~/stores/screening/model/screening.model'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Edit, Loader2, Save, AlertTriangle } from 'lucide-vue-next'
import { useToast } from '@/components/ui/toast/use-toast'

const screeningStore = useScreeningStore()
const { toast } = useToast()
const isDialogOpen = ref(false)
const selectedQuestion = ref<QuestionItem | null>(null)
const editedQuestion = ref<QuestionItem | null>(null)
const isSaving = ref(false)


// Use useAsyncData for better async handling
const { data: questions, pending, refresh, error } = useAsyncData(
  'screening-questions',
  () => screeningStore.fetchQuestions(),
  {
    server: false,
    lazy: true,
    // watch: [() => screeningStore.questionsState]
  }
)

// Computed property to access the questions from the store
const screeningQuestions = computed(() => screeningStore.questions)

// Open edit dialog with a deep copy of the question to edit
const openEditDialog = (question: QuestionItem, groupId: string, categoryId: string) => {
  // Create a deep copy to avoid modifying the original data until save
  selectedQuestion.value = {
    ...question,
    answers: question.answers.map(a => ({
      ...a,
      option: { ...a.option }
    })),
    question: { ...question.question },
    _groupId: groupId,
    _categoryId: categoryId
  }
  isDialogOpen.value = true
}

// Save the edited question
const saveQuestion = async () => {
  if (!selectedQuestion.value) return
  
  isSaving.value = true
  try {
    // Call the update method from the store
    await screeningStore.updateQuestion(
      selectedQuestion.value._categoryId,
      selectedQuestion.value._groupId,
      selectedQuestion.value
    )
    
    toast({
      title: "Question updated",
      description: "The question has been successfully updated.",
    })
    
    // Refresh the questions list
    refresh()
    isDialogOpen.value = false
  } catch (err) {
    toast({
      title: "Update failed",
      description: "There was an error updating the question.",
      variant: "destructive",
    })
    console.error(err)
  } finally {
    isSaving.value = false
  }
}

// Cancel editing
const cancelEdit = () => {
  selectedQuestion.value = null
  isDialogOpen.value = false
}
</script>

<template>
  <div class="container mx-auto p-6">
    <header class="mb-8">
      <h1 class="text-2xl font-bold">Screening Questions</h1>
      <p class="text-muted-foreground">Manage screening questions and answers for client portals</p>
    </header>

    <!-- Loading State -->
    <div v-if="pending" class="flex items-center justify-center py-12">
      <Loader2 class="h-8 w-8 animate-spin text-primary" />
      <span class="ml-2 text-muted-foreground">Loading questions...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="rounded-lg border border-destructive/50 bg-destructive/10 p-6 text-center">
      <AlertTriangle class="mx-auto h-10 w-10 text-destructive" />
      <h3 class="mt-2 text-lg font-medium">Failed to load questions</h3>
      <p class="mt-1 text-sm text-muted-foreground">{{ error.message }}</p>
      <Button variant="outline" class="mt-4" @click="refresh">Try Again</Button>
    </div>

    <!-- Questions List -->
    <div v-else-if="screeningQuestions.length > 0" class="space-y-8">
      <div v-for="categoryGroup in screeningQuestions" :key="categoryGroup.category.id" class="overflow-hidden rounded-lg border shadow-sm">
        <!-- Category Header -->
        <div class="border-b bg-muted/50 p-4">
          <h2 class="text-lg font-semibold">{{ categoryGroup.category.name }}</h2>
          <p class="text-sm text-muted-foreground">{{ categoryGroup.category.l10n_key }}</p>
        </div>

        <!-- Questions -->
        <div class="divide-y">
          <div v-for="group in categoryGroup.question_group" :key="group.id" class="p-4">
            <div class="mb-2 flex items-center">
              <h3 class="text-sm font-medium text-muted-foreground">Group ID: {{ group.id }}</h3>
              <div v-if="group.description" class="ml-2 text-xs text-muted-foreground">{{ group.description }}</div>
            </div>
            
            <div v-for="item in group.questions" :key="item.question.id" class="mb-4 rounded-md border bg-card p-4 shadow-sm">
              <div class="flex items-start justify-between gap-4">
                <div class="flex-1">
                  <p class="font-medium">{{ item.question.name }}</p>
                  <p class="text-xs text-muted-foreground">{{ item.question.l10n_key }}</p>
                  
                  <div class="mt-3">
                    <h4 class="mb-1 text-xs font-medium uppercase text-muted-foreground">Answers</h4>
                    <div class="space-y-2">
                      <div 
                        v-for="answer in item.answers" 
                        :key="answer.id"
                        class="flex items-center gap-2 text-sm"
                      >
                        <span class="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                          {{ answer.option.name }}
                        </span>
                        <span v-if="answer.action" class="text-xs">
                          Action: 
                          <span :class="{
                            'text-green-500': answer.action === 'no_action',
                            'text-blue-500': answer.action === 'monitoring_action',
                            'text-orange-500': answer.action === 'refer_action'
                          }">
                            {{ screeningStore.getActionDescriptionByName(answer.action) }}
                          </span>
                        </span>
                        <span v-if="answer.redirect" class="text-xs">
                          Redirect: {{ answer.redirect }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  @click="openEditDialog(item, group.id, categoryGroup.category.id)"
                  class="text-muted-foreground hover:text-foreground"
                >
                  <Edit class="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="rounded-lg border border-dashed p-12 text-center">
      <h3 class="text-lg font-medium">No questions found</h3>
      <p class="mt-1 text-sm text-muted-foreground">There are no screening questions available.</p>
      <Button variant="outline" class="mt-4" @click="refresh">Refresh</Button>
    </div>

    <!-- Edit Dialog -->
    <Dialog v-model:open="isDialogOpen">
      <DialogContent class="sm:max-w-[550px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Edit Question</DialogTitle>
          <DialogDescription>
            Make changes to the question and its answers. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
        
        <div v-if="selectedQuestion" class="grid gap-4 py-4 overflow-y-auto pr-2">
          <div class="grid gap-2">
            <label for="question-text" class="text-sm font-medium">Question Text</label>
            <Input
              id="question-text"
              v-model="selectedQuestion.question.name"
              placeholder="Enter question text"
            />
          </div>
          
          <div class="grid gap-2">
            <label for="question-key" class="text-sm font-medium">Localization Key</label>
            <Input
              id="question-key"
              v-model="selectedQuestion.question.l10n_key"
              placeholder="Enter localization key"
            />
          </div>
          
          <div class="grid gap-2">
            <label class="text-sm font-medium">Answers</label>
            <div v-for="(answer, index) in selectedQuestion.answers" :key="answer.id" class="grid gap-2 rounded-md border p-3">
              <div class="grid grid-cols-2 gap-2">
                <div>
                  <label :for="`answer-text-${index}`" class="mb-1 block text-xs">Answer Text</label>
                  <Input
                    :id="`answer-text-${index}`"
                    v-model="answer.option.name"
                    placeholder="Answer text"
                  />
                </div>
                <div>
                  <label :for="`answer-key-${index}`" class="mb-1 block text-xs">Localization Key</label>
                  <Input
                    :id="`answer-key-${index}`"
                    v-model="answer.option.l10n_key"
                    placeholder="Localization key"
                  />
                </div>
              </div>
              
              <div class="grid grid-cols-2 gap-2">
                <div>
                  <label :for="`answer-action-${index}`" class="mb-1 block text-xs">Action</label>
                  <Select v-model="answer.action">
                    <SelectTrigger :id="`answer-action-${index}`">
                      <SelectValue placeholder="Select action" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="no_action">
                        No action required
                      </SelectItem>
                      <SelectItem value="monitoring_action">
                        Refer for further monitoring and teaching adaptation
                      </SelectItem>
                      <SelectItem value="refer_action">
                        Refer for further assessment
                      </SelectItem>
                      <SelectItem value="null">None</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label :for="`answer-redirect-${index}`" class="mb-1 block text-xs">Redirect</label>
                  <Input
                    :id="`answer-redirect-${index}`"
                    v-model="answer.redirect"
                    placeholder="Redirect ID (optional)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter class="mt-auto pt-4 border-t">
          <Button variant="outline" @click="cancelEdit">Cancel</Button>
          <Button @click="saveQuestion" :disabled="isSaving">
            <Loader2 v-if="isSaving" class="mr-2 h-4 w-4 animate-spin" />
            <Save v-else class="mr-2 h-4 w-4" />
            {{ isSaving ? 'Saving...' : 'Save Changes' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>