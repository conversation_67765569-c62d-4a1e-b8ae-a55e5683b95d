import { defineStore } from 'pinia'
import { useUserStore } from '../auth/user/user.store'
import { ApiResponseState } from '~/utils/enum/apiResponse.enum'
import { type FilterProps } from '@/utils/type/filterProps'

enum DisabilityStatus {
    ND = '1',
    PSMDO = '2',
    PMDO = '3',
    RFMTA = '4'
}

interface PhoneNumber {
    number: string
    countryCode: string
}

interface Guardian {
    id: string
    email: string
    notes: string
    last_name: string
    created_at: string
    first_name: string
    phone_number: PhoneNumber
}

interface School {
    id: string
    name: string
}

interface Grade {
    id: number
    name: string
    age_range: string
    description: string
    education_level_id: number
}

export interface Location {
    id: string
    name: string
}

export interface Country extends Location {
    country_code: string
}

export interface Child {
    id: string
    first_name: string
    last_name: string
    middle_name: string
    age: number
    screened: boolean
    last_screened: string
    gender: string
    country: Country
    region: Location
    district: Location
    school: School
    grade: Grade
    guardian: Guardian
    avatar_url: string
}

interface ChildrenResponse {
    data: Child[]
    page: number
    limit: number
    count: number
}

interface AgeDistribution {
    [key: string]: number
}

interface DisabilityStatusCount {
    [DisabilityStatus.ND]?: number     // Non Detected
    [DisabilityStatus.PSMDO]?: number  // Potential Single Disability Occurrence
    [DisabilityStatus.PMDO]?: number   // Potential Multiple Disability Occurrences
    [DisabilityStatus.RFMTA]?: number  // Refer For Further Monitoring and Teaching Adaptation
}

interface ChildrenStats {
    total_children: number
    total_screened: number
    screening_rate: number
    age_distribution: AgeDistribution
    disability_status_count: DisabilityStatusCount
}

interface ChildrenState {
    allChildrenSelected: boolean
    children: Child[],
    selectedChildren: Child[],
    stats: ChildrenStats | null
    currentPage: number
    totalPages: number
    totalItems: number
    isLoading: boolean
    error: string | null
    apiState: ApiResponseState
    selectedChild: Child | null
    grades: Grade[]
}

// First, let's add the interface for the create child payload
export interface CreateChildPayload {
    school_id: string
    first_name: string
    last_name: string
    date_of_birth: string
    gender: string
    org_id: string
    district_id: string
    grade_id: string
    middle_name: string | null
    guardian: {
        id: string
        first_name: string
        last_name: string
        email: string
        phone_number: {
            countryCode: string
            number: string
        }
        notes: string
    }
}


export const useChildrenStore = defineStore('children', {
    state: (): ChildrenState => ({
        children: [],
        allChildrenSelected: false,
        selectedChildren: [],
        stats: null,
        currentPage: 1,
        totalPages: 0,
        totalItems: 0,
        isLoading: false,
        error: null,
        apiState: ApiResponseState.NULL,
        selectedChild: null,
        grades: [] as Grade[]
    }),

    actions: {
        async fetchChildrenStats() {
            this.apiState = ApiResponseState.LOADING

            try {
                const stats = await $fetch<ChildrenStats>('/organization/child/stats', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                this.stats = stats
                this.apiState = ApiResponseState.SUCCESS
                return this.stats
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch children stats'
                this.error = errorMessage
                this.apiState = ApiResponseState.FAILED
                console.error('Error fetching children stats:', error)
                throw new Error(errorMessage)
            }
        },

        async fetchChildren(page = 1, limit = 20, filters?: FilterProps) {
            this.isLoading = true

            // Build query parameters
            const query: any = {
                page,
                limit: this.allChildrenSelected ? -1 : limit
            }

            // Only add filter parameters if they have values
            if (filters) {
                Object.entries(filters).forEach(([key, value]) => {
                    if (value) {
                        query[`filter[${key}]`] = value
                    }
                })
            }


            try {
                const response = await $fetch<ChildrenResponse>('/child/', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    query
                })

                this.children = response.data
                this.currentPage = response.page
                this.totalItems = response.count
                this.totalPages = Math.ceil(response.count / limit)
                return response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch children'
                this.error = errorMessage
                console.error('Error fetching children:', error)
                throw new Error(errorMessage)
            } finally {
                this.isLoading = false
            }
        },

        resetState() {
            this.children = []
            this.stats = null
            this.currentPage = 1
            this.totalPages = 0
            this.totalItems = 0
            this.isLoading = false
            this.error = null
            this.apiState = ApiResponseState.NULL
            this.selectedChild = null
        },

        async createChild(payload: CreateChildPayload) {
            this.apiState = ApiResponseState.LOADING

            try {
                const response = await $fetch<Child>('/child/', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    body: payload
                })

                this.apiState = ApiResponseState.SUCCESS
                return response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create child'
                this.error = errorMessage
                this.apiState = ApiResponseState.FAILED
                console.error('Error creating child:', error)
                throw new Error(errorMessage)
            }
        },

        async bulkCreateChild(payload: CreateChildPayload[]) {
            this.apiState = ApiResponseState.LOADING

            try {
                const response = await $fetch<Child>('/child/bulk', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    body: { children: payload }
                })

                this.apiState = ApiResponseState.SUCCESS
                return response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create children'
                this.error = errorMessage
                this.apiState = ApiResponseState.FAILED
                console.error('Error creating children:', error)
                throw new Error(errorMessage)
            }
        },

        async fetchGrades() {
            try {
                const response = await $fetch<any>('/organization/grades', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                return this.grades = response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch grades'
                this.error = errorMessage
                console.error('Error fetching grades:', error)
                throw new Error(errorMessage)
            }
        },

        async deleteChild(childId: String) {
            try {
                const response = await $fetch<any>(`/child/${childId}`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                return true
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete child and associated records'
                this.error = errorMessage
                console.error('Failed to delete child and associated records:', error)
                throw new Error(errorMessage)
            }
        },

        async deleteMultipleChildren(childrenId: String[]) {
            try {
                const response = await $fetch<any>(`/child/bulk/`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    body: { childIds: childrenId }
                })

                return true
            } catch (error) {
                console.log(error)
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete children and associated records'
                this.error = errorMessage
                console.error('Failed to delete children and associated records:', error)
                throw new Error(errorMessage)
            }
        },

        toggleSelectAll(value: boolean) {
            this.allChildrenSelected = value
        },

        selectChildren(children: Child[]) {
            this.selectedChildren = children
        },

        selectChild(child: Child) {
            this.selectedChild = child
        },

        resetSelectedChildren() {
            this.selectedChildren = []
        },

        async exportChildrenStreamCSV() {
            try {
                // Build the URL with query parameters
                const baseURL = "https://fgfszeyuebbunceukopg.supabase.co";
                const url = new URL("/functions/v1/exportChildrenCSV", baseURL);

                url.searchParams.append("filter[org_id]", useUserStore().currentUser?.organization?.id ?? "");
                url.searchParams.append("org_name", useUserStore().currentUser?.organization?.name ?? "");

                // Create a temporary link to trigger download
                const link = document.createElement('a');
                link.href = url.toString();
                link.download = `children_export_${Date.now()}.csv`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                return true;
            } catch (error) {
                console.error('CSV Export Error:', error);
                throw new Error('Failed to export children');
            }
        },

        exportToCSV() {
            return useExportToCSV({
                fileName: `Disability_Detect_Children_${Date.now()}`,
                header: ["Id", "Name", "Age", "Class", "Gender", "Guardian", "Guardian_Email", "Guardian_PhoneNumber", "Notes", "Location", "Country"],
                records: this.transformChildrenToCSVData()
            })
        },
        transformChildrenToCSVData(): any[] {
            const csvData: any[] = [];

            const childData = this.selectedChildren.length > 0 ? this.selectedChildren : this.children;

            // Transform each child object into the desired format
            childData.forEach(child => {
                const {
                    id,
                    first_name,
                    last_name,
                    middle_name,
                    age,
                    district,
                    region,
                    gender,
                    guardian,
                    grade
                } = child;

                // Format the "Name" column
                const name = middle_name
                    ? `${first_name} ${middle_name} ${last_name}`
                    : `${first_name} ${last_name}`;

                // Push the transformed values into the CSV data array
                csvData.push({
                    ID: id,
                    Name: name,
                    Age: age,
                    Class: grade?.name,
                    Gender: gender,
                    Guardian: guardian?.first_name + " " + guardian?.last_name,
                    Guardian_Email: guardian?.email,
                    Guardian_Phone: guardian?.phone_number.number,
                    Guardian_Notes: guardian?.notes,
                    District: district?.name,
                    Region: region?.name,
                });
            });

            return csvData;
        },
    },

    getters: {
        hasStats: (state): boolean => state.apiState === ApiResponseState.SUCCESS && !!state.stats,
        hasChildren: (state): boolean => state.children.length > 0,
        screeningRate: (state): number => state.stats?.screening_rate ?? 0,
        totalScreened: (state): number => state.stats?.total_screened ?? 0,
        getDisabilityLabel: () => (status: string): string => {
            const labels: any = {
                [DisabilityStatus.ND]: 'Non Detected (ND)',
                [DisabilityStatus.PSMDO]: 'Potential Single Disability Occurrence (PSMDO)',
                [DisabilityStatus.PMDO]: 'Potential Multiple Disability Occurrences (PMDO)',
                [DisabilityStatus.RFMTA]: 'Refer For Further Monitoring and Teaching Adaptation (RFMTA)'
            }
            return labels[status] || status
        }
    }
})

