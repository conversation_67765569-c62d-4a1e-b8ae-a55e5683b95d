import { defineStore } from 'pinia'
import { useUserStore } from '../auth/user/user.store'
import { ApiResponseState } from '~/utils/enum/apiResponse.enum'

interface OrganizationStats {
    total_children: number
    total_schools: number
    total_agents: number
    total_screening_results: number
    total_referred_screenings: number
    last_updated?: string
}

interface DailyScreeningCount {
    count: number
}

interface OrganizationState {
    stats: OrganizationStats | null
    dailyScreenings: DailyScreeningCount | null
    isLoadingStats: boolean
    isLoadingDailyCount: boolean
    error: string | null
    lastFetchedAt: string | null
    apiState: ApiResponseState
    countries: Country[]
    regions: Location[]
    districts: Location[]
}

export interface Location {
    id: string
    name: string
}

export interface Country extends Location {
    country_code: string
}

export const useOrganizationStore = defineStore('organization', {
    state: (): OrganizationState => ({
        stats: null,
        dailyScreenings: null,
        isLoadingStats: false,
        isLoadingDailyCount: false,
        error: null,
        lastFetchedAt: null,
        apiState: ApiResponseState.NULL,
        countries: [] as Country[],
        regions: [] as Country[],
        districts: [] as Country[],
    }),

    actions: {
        async fetchStats() {
            this.apiState = ApiResponseState.LOADING

            try {
                const stats = await $fetch<OrganizationStats>('/organization/stats', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                this.stats = stats
                this.apiState = ApiResponseState.SUCCESS

                return this.stats
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch organization stats'
                this.error = errorMessage
                this.apiState = ApiResponseState.FAILED
                console.error('Error fetching organization stats:', error)
                throw new Error(errorMessage)
            }
        },

        async fetchDailyScreeningCount() {
            this.isLoadingDailyCount = true

            try {
                const result = await $fetch<DailyScreeningCount>('/screening/org/count/daily', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                this.dailyScreenings = result
                return result.count
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch daily screening count'
                console.error('Error fetching daily screening count:', error)
                throw new Error(errorMessage)
            } finally {
                this.isLoadingDailyCount = false
            }
        },

        async fetchCountries(): Promise<Country[]> {
            try {
                const response = await $fetch<any>('/general/countries', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                })
                return this.countries = response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch countries'
                this.error = errorMessage
                console.error('Error fetching countries:', error)
                throw new Error(errorMessage)
            }
        },

        async fetchRegions(countryId: string) {
            try {
                const response = await $fetch<any>(`/general/regions/${countryId}`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                return this.regions = response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch regions'
                this.error = errorMessage
                console.error('Error fetching regions:', error)
                throw new Error(errorMessage)
            }
        },

        async fetchDistricts(regionId: string) {
            try {
                const response = await $fetch<any>(`/general/districts/${regionId}`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                return this.districts = response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch districts'
                this.error = errorMessage
                console.error('Error fetching districts:', error)
                throw new Error(errorMessage)
            }
        },

        async fetchSchoolTypes() {
            try {
                const response = await $fetch<any>(`/school/types`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                return response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch districts'
                this.error = errorMessage
                console.error('Error fetching districts:', error)
                throw new Error(errorMessage)
            }
        },

        resetState() {
            this.stats = null
            this.dailyScreenings = null
            this.isLoadingStats = false
            this.isLoadingDailyCount = false
            this.error = null
            this.lastFetchedAt = null
            this.apiState = ApiResponseState.NULL
        }
    },

    getters: {
        hasStats: (state): boolean => state.apiState === ApiResponseState.SUCCESS && !!state.stats,
        todayScreeningCount: (state): number => state.dailyScreenings?.count ?? 0
    }
})