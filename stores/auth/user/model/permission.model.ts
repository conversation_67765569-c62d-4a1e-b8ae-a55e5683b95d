export interface IPermission {
    id: number;
    name: string;
    description: string;
    resource: string;
}

export class PermissionModel {
    permission: IPermission;
    constructor(permission: IPermission) {
        this.permission = permission;
    }

    static fromMap(json: any): IPermission {
        return {
            id: json.id,
            name: json.name,
            description: json.description,
            resource: json.resource
        };
    }
}