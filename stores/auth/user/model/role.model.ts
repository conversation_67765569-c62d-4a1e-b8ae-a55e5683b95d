import type { IPermission } from "./permission.model";



export interface IRole {
    name:        string;
    createdAt:   string;
    description: string;
    permissions: IPermission[];
    id:    number;
}


export class RoleModel{
    role: IRole;
    constructor(role: IRole) {
        this.role = role;
    }

    static fromMap(json: any) {
        const role: IRole = {
            id: json.id,
            name : json.name,
            description : json.description,
            permissions : json.permissions,
            createdAt : json.createdAt

        };

        return role;
    }
}