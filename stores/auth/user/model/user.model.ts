

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatarUrl?: string;
  role?: {
    id: string;
    name: string;
    permissions: {
      id: string;
      name: string;
    }[];
  };
  organization?: {
    id: string;
    name: string;
  };
  country?: {
    id: string;
    name: string;
    countryCode: string;
  };
}

export class UserModel {
  user: User;

  constructor(user: User) {
    const defaultAvatarUrl = `https://api.dicebear.com/6.x/initials/svg?seed=${user.firstName} ${user.lastName}`;
    if (!user.avatarUrl) {
      user.avatarUrl = defaultAvatarUrl;
    }
    this.user = user;
  }

  static default(): UserModel {
    const user: User = {
      id: "",
      firstName: "",
      lastName: "",
      email: "",
      avatarUrl: "",
      role: {
        id: "",
        name: "",
        permissions: []
      },
      organization: {
        id: "",
        name: ""
      },
      country: {
        id: "",
        name: "",
        countryCode: ""
      }
    };

    return new UserModel(user);
  }

  static fromMap(json: any): User {
    return {
      id: json.id,
      firstName: json.first_name,
      lastName: json.last_name,
      email: json.email,
      avatarUrl: json.avatar_url,
      role: json.role ? {
        id: json.role.id,
        name: json.role.name,
        permissions: json.role.permissions
      } : undefined,
      organization: json.org ? {
        id: json.org.id,
        name: json.org.name
      } : undefined,
      country: json.country ? {
        id: json.country.id,
        name: json.country.name,
        countryCode: json.country.country_code
      } : undefined
    };
  }
}

