import { defineStore } from 'pinia'
import { useUserStore } from '../auth/user/user.store'
import { ApiResponseState } from '~/utils/enum/apiResponse.enum'
import type { IRole } from '../auth/user/model/role.model'
import type { IPermission, IPermissionTag } from '../auth/user/model/permission.model'

interface PermissionsState {
  roles: IRole[]
  permissions: IPermission[]
  isLoadingRoles: boolean
  isLoadingPermissions: boolean
  error: string | null
  rolesState: ApiResponseState
  permissionsState: ApiResponseState
}

export const usePermissionsStore = defineStore('permissions', {
  state: (): PermissionsState => ({
    roles: [],
    permissions: [],
    isLoadingRoles: false,
    isLoadingPermissions: false,
    error: null,
    rolesState: ApiResponseState.NULL,
    permissionsState: ApiResponseState.NULL
  }),

  actions: {
    async fetchRoles() {
      this.isLoadingRoles = true
      this.error = null
      this.rolesState = ApiResponseState.LOADING

      try {
        const { data } = await useFetch<IRole[]>('/permissions/roles', {
          baseURL: useRuntimeConfig().public.API_BASE_URL,
          headers: {
            'Authorization': `Bearer ${useUserStore().token}`
          }
        })

        if (data.value) {
          this.roles = data.value
          this.rolesState = ApiResponseState.SUCCESS
        }
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Failed to fetch roles'
        this.rolesState = ApiResponseState.FAILED
        console.error('Error fetching roles:', error)
      } finally {
        this.isLoadingRoles = false
      }
    },

    async fetchPermissions() {
      this.isLoadingPermissions = true
      this.permissionsState = ApiResponseState.LOADING

      try {
        console.log("GETTING ALL PERMISSIONS")
        const { data } = await useFetch<IPermission[]>('/permissions/', {
          baseURL: useRuntimeConfig().public.API_BASE_URL,
          headers: {
            'Authorization': `Bearer ${useUserStore().token}`
          }
        })

        console.log("PERMISSIONS: ", data.value)

        if (data.value) {
          this.permissions = data.value
          this.permissionsState = ApiResponseState.SUCCESS
        }
      } catch (error) {
        this.error = error instanceof Error ? error.message : 'Failed to fetch permissions'
        this.permissionsState = ApiResponseState.FAILED
        console.error('Error fetching permissions:', error)
      } finally {
        this.isLoadingPermissions = false
      }
    },

    async createRole(role: Partial<IRole>) {
      try {
        const response = await $fetch('/roles', {
          method: 'POST',
          baseURL: useRuntimeConfig().public.API_BASE_URL,
          headers: {
            'Authorization': `Bearer ${useUserStore().token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(role)
        })
        
        await this.fetchRoles()
        return response
      } catch (error) {
        console.error('Error creating role:', error)
        throw error
      }
    },

    async updateRole(roleId: number, role: Partial<IRole>) {
      try {
        const permissionIds = role.permissions?.map(p => p.id) || []
        
        const response = await $fetch(`/permissions/roles/${roleId}`, {
          method: 'PUT',
          baseURL: useRuntimeConfig().public.API_BASE_URL,
          headers: {
            'Authorization': `Bearer ${useUserStore().token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ permissionIds })
        })
        
        await this.fetchRoles()
        return response
      } catch (error) {
        console.error('Error updating role:', error)
        throw error
      }
    },

    async deleteRole(roleId: string) {
      try {
        const response = await $fetch(`/roles/${roleId}`, {
          method: 'DELETE',
          baseURL: useRuntimeConfig().public.API_BASE_URL,
          headers: {
            'Authorization': `Bearer ${useUserStore().token}`
          }
        })
        
        await this.fetchRoles()
        return response
      } catch (error) {
        console.error('Error deleting role:', error)
        throw error
      }
    }
  },

  getters: {
    hasRoles: (state) => state.rolesState === ApiResponseState.SUCCESS && state.roles.length > 0,
    hasPermissions: (state) => state.permissionsState === ApiResponseState.SUCCESS && state.permissions.length > 0,
    isLoadingAny: (state) => state.isLoadingRoles || state.isLoadingPermissions,
    permissionsByModule: (state) => {
      const grouped: Record<string, IPermission[]> = {}
      
      state.permissions.forEach(permission => {
        const moduleName = permission.tag?.name || 'Other'
        if (!grouped[moduleName]) {
          grouped[moduleName] = []
        }
        grouped[moduleName].push(permission)
      })
      
      return grouped
    }
  }
})