export interface Category {
    id: string
    name: string
    l10n_key: string
}

export interface Option {
    id: number
    name: string
    l10n_key: string
}

export interface Answer {
    id: string
    action: string | null
    option: Option
    redirect: string | null
}

export interface Question {
    id: string
    name: string
    l10n_key: string
}

export interface QuestionItem {
    answers: Answer[]
    question: Question
}

export interface QuestionGroup {
    id: string
    questions: QuestionItem[]
    description: string | null
}

export interface ScreeningQuestion {
    category: Category
    question_group: QuestionGroup[]
}