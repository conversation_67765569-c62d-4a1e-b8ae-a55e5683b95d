import { defineStore } from 'pinia'
import { useUserStore } from '../auth/user/user.store'
import type { ScreeningQuestion } from './model/screening.model'
import { ApiResponseState } from '~/utils/enum/apiResponse.enum'
import { getQuestion } from '@/utils/questions'
import { generateScreeningPdf } from '@/utils/export/pdf'
import { type FilterProps } from '@/utils/type/filterProps'

export interface ScreeningTrendData {
    screening_date: string;
    screening_count: number;
}

export interface ScreeningResult {
    id: string
    child_id: {
        id: string
        last_name: string
        first_name: string
    }
    overall_disability_status: number
    created_at: string
    created_by: {
        id: string
        last_name: string
        first_name: string
    }
    survey_responses: Array<{
        category: {
            id: string
            l10nKey: string
        }
        responses: Array<{
            answer: {
                id: string
                l10nKey: string
            }
            question: {
                id: string
                l10nKey: string
            }
        }>
    }>
    analysis_result: {
        rfaCount: number
        rfmCount: number
        overallStatus: number
        categoryResults: Record<string, string>
    }
    org_id: string
}

interface ScreeningResultsResponse {
    data: ScreeningResult[]
    page: number
    limit: number
    count: number
}



interface ScreeningState {
    questions: ScreeningQuestion[]
    isLoadingQuestions: boolean
    screeningResults: ScreeningResult[]
    selectedScreeningResults: ScreeningResult[]
    isLoadingResults: boolean
    currentPage: number
    totalResults: number
    resultsPerPage: number
    error: string | null
    questionsState: ApiResponseState
    resultsState: ApiResponseState
    trendData: ScreeningTrendData[]
    isTrendLoading: boolean
    trendError: string | null
}

interface TopReferredDistrictType {
    id: string;
    name: string;
    count: number;
    region:{
        id: string;
        name: string;
    }
}

interface TopReferredSchoolType {
    id: string;
    name: string;
    count: number;
    district:{
        id: string;
        name: string;
    }
}

export const useScreeningStore = defineStore('screening', {
    state: (): ScreeningState => ({
        questions: [],
        isLoadingQuestions: false,
        screeningResults: [],
        selectedScreeningResults : [],
        isLoadingResults: false,
        currentPage: 1,
        totalResults: 0,
        resultsPerPage: 10,
        error: null,
        questionsState: ApiResponseState.NULL,
        resultsState: ApiResponseState.NULL,
        trendData: [],
        isTrendLoading: false,
        trendError: null
    }),

    actions: {
        async fetchQuestions() {
            this.isLoadingQuestions = true
            this.error = null
            this.questionsState = ApiResponseState.LOADING

            try {
                const { data } = await useFetch<ScreeningQuestion[]>('/screening/questions/algo_update', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                if (data.value) {
                    this.questions = data.value
                    this.questionsState = ApiResponseState.SUCCESS
                }
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to fetch screening questions'
                this.questionsState = ApiResponseState.FAILED
                console.error('Error fetching screening questions:', error)
            } finally {
                this.isLoadingQuestions = false
            }
        },


        async updateQuestion(categoryId: string, groupId: string, updatedQuestion: any) {

            try {
                // Transform the question data to match the expected format
                const preparedQuestion = {
                    // ...updatedQuestion,
                    categoryId,
                    groupId,
                    answers: updatedQuestion.answers.map((answer:any) => ({
                        ...answer,
                        // Convert action to object format if it's a string ID
                        action: answer.action ?? null
                    }))
                };


                const response = await $fetch(`/screening/questions`, {
                    method: 'PUT',
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(preparedQuestion)
                })

                // Update the local state
                const categoryIndex = this.questions.findIndex(c => c.category.id === categoryId)
                if (categoryIndex !== -1) {
                    const groupIndex = this.questions[categoryIndex].question_group.findIndex(g => g.id === groupId)
                    if (groupIndex !== -1) {
                        const questionIndex = this.questions[categoryIndex].question_group[groupIndex].questions.findIndex(
                            q => q.question.id === updatedQuestion.question.id
                        )
                        if (questionIndex !== -1) {
                            this.questions[categoryIndex].question_group[groupIndex].questions[questionIndex] = updatedQuestion
                        }
                    }
                }

                return response
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to update question'
                console.error('Error updating question:', error)
                throw error
            }
        },

        // Helper method to get action name by ID
        getActionNameById(actionId: string): string {
            const actionMap: Record<string, string> = {
                '85553c96-6383-443c-b998-16823b9af5b': 'no_action',
                '9057117d-f451-49fc-93d6-cc59df456e3d': 'monitoring_action',
                'a9d14c0f-ac0d-4b2c-8218-acfc793dbc20': 'refer_action'
            };

            return actionMap[actionId] || '';
        },

         // Helper method to get action description by name
         getActionDescriptionByName(name: string): string {
            const actionMap: Record<string, string> = {
                'no_action': 'No action required',
                'monitoring_action': 'Refer for further monitoring and teaching adaptation',
                'refer_action': 'Refer for further assessment'
            };

            return actionMap[name] || '';
        },



        async fetchScreeningResults(page = 1, limit = 10, filters?:FilterProps) {

            try {
                 // Build query parameters
                 const query: any = {
                    page,
                    limit
                }

                // Only add filter parameters if they have values
                if (filters) {
                    Object.entries(filters).forEach(([key, value]) => {
                        if (value) {
                            query[`filter[${key}]`] = value
                        }
                    })
                }

                const response = await $fetch<ScreeningResultsResponse>('/screening/result', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    query
                })

                this.screeningResults  = response.data
                this.currentPage = response.page
                this.totalResults = response.count

                return response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch screenings'
                this.error = errorMessage
                console.error('Error fetching screenings:', error)
                throw new Error(errorMessage)
            }

        },

        selectScreeningResults(screenings: ScreeningResult[]) {
            this.selectedScreeningResults = screenings
        },

        getDisabilityStatusText(status: number): string {
            switch (status) {
                case 1: return 'None detected'
                case 2: return 'Potential Single Disability'
                case 3: return 'Potential Multiple Disabilities'
                case 5: return 'Refer For Monitoring'
                default: return 'Unknown Status'
            }
        },
        isReferred(status: number){
            return status === 5 || status === 3 || status === 2
        },
        getDisabilityStatusDescription(status: number): string {
            switch (status) {
                case 1: return 'No disability detected in this screening'
                case 2: return 'Potential single disability occurrence detected'
                case 3: return 'Potential multiple disability occurrences detected'
                case 5: return 'Refer for further monitoring and teaching adaptation'
                default: return 'Status information not available'
            }
        },
        getDisabilityStatusClass(status: number): string {
            switch (status) {
                case 1: return 'text-green-800 bg-green-100'
                case 2: return 'text-red-800 bg-red-100'
                case 3: return 'text-red-800 bg-red-100'
                case 5: return 'text-yellow-800 bg-yellow-100'
                default: return 'text-gray-800 bg-gray-100'
            }
        },
        getQuestionMeta(l10nKey: string): { cfId: string; questionNumber: number } | null {
            const match = l10nKey.match(/^(cf\d+)_question_(\d+)$/)
            if (!match) return null

            return {
                cfId: match[1],
                questionNumber: parseInt(match[2])
            }
        },
        getCategoryLongName(categoryKey: string): string {
            const categoryMap: Record<string, string> = {
                'categoryVision': 'Vision',
                'categoryLiteracy': 'Literacy & Numeracy Skills',
                'categoryInattention': 'Inattention, Hyperactivity & Impulsivity',
                'categoryHearing': 'Hearing',
                'categoryMobility': 'Mobility or Motor Skills',
                'categorySocialInteraction': 'Social Interaction Skills',
                'categoryCommunication': 'Communication Skills'
            }

            return categoryMap[categoryKey] || 'Unknown Category'
        },
        getDisabilityAnalysisResultDescription(status: string): string {
            const statusMap: Record<string, string> = {
                'NODIFFICULTY': 'No Action Required',
                'DIFFICULTY': 'Requires Further Monitoring & Teaching Adaption (!)',
                'DIFFICULTYPLUS': 'Refer for Further Assessment and Requires IEP (!!)',
                'CANNOT': 'Refer For Further Assessment and Requires IEP (!!!)'
            }

            return statusMap[status] || 'Unknown Status'
        },
        getDisabilityAnalysisResultClass(status: string): string {
            const classMap: Record<string, string> = {
                'NODIFFICULTY': 'text-green-800 bg-green-100 ',
                'DIFFICULTY': 'text-yellow-800 bg-yellow-100 ',
                'DIFFICULTYPLUS': 'text-red-800 bg-red-100 ',
                'CANNOT': 'text-red-800 bg-red-100 '
            }

            return classMap[status] || 'text-gray-800 bg-gray-100 border border-gray-200'
        },
        getAnswerDisplayValue(l10nKey: string): string {
            const answerMap: Record<string, string> = {
                'answerYes': 'Yes',
                'answerNo': 'No',
                'answerNoDifficulty': 'No difficulty',
                'answerSomeDifficulty': 'Some difficulty',
                'answerALotOfDifficulty': 'A lot of difficulty',
                'answerCannotDoAtAll': 'Cannot do it at all',
                'answerNever': 'Never',
                'answerOften': 'Often',
                'answerSometimes': 'Sometimes',
                'answerAlways': 'Always'
            }

            return answerMap[l10nKey] || l10nKey
        },
        exportToCSV() {
            return useExportToCSV({
                fileName: `Disability_Detect_Screening_${Date.now()}`,
                header: ["Id", "Name", "Screening Date", "Screener", "Overall Status", "Responses"],
                records: this.transformScreeningToCSVData()
            })
        },
        exportSelectedChildScreeningToCSV(screening: ScreeningResult) {
            return useExportToCSV({
                fileName: `${screening.child_id.first_name}_${screening.child_id.last_name}_Disability_Detect_Screening_${Date.now()}`,
                header: ["Id", "Name", "Screening Date", "Screener", "Overall Status", "Responses"],
                records: [
                    {
                        Id: screening.id,
                        Name: `${screening.child_id.first_name} ${screening.child_id.last_name}`,
                        "Screening Date": new Date(screening.created_at).toLocaleDateString(),
                        Screener: `${screening.created_by.first_name} ${screening.created_by.last_name}`,
                        "Overall Status": this.getDisabilityStatusDescription(screening.analysis_result.overallStatus),
                        Responses :  screening.survey_responses.map(response => {
                            return `${this.getCategoryLongName(response.category.l10nKey)}: ${response.responses.map(r => `${r.question.l10nKey }: ${this.getAnswerDisplayValue(r.answer.l10nKey)}`).join(', ')}`;
                        }).join('; ')
                    }
                ]
            })
        },
        transformScreeningToCSVData(): any[] {
            const csvData: any[] = [];

            const screeningData = this.selectedScreeningResults.length > 0 ? this.selectedScreeningResults : this.screeningResults;

            // Transform each screening result into CSV format
            screeningData.forEach(screening => {
                csvData.push({
                    Id: screening.id,
                    Name: `${screening.child_id.first_name} ${screening.child_id.last_name}`,
                    "Screening Date": new Date(screening.created_at).toLocaleDateString(),
                    Screener: `${screening.created_by.first_name} ${screening.created_by.last_name}`,
                    "Overall Status": this.getDisabilityStatusDescription(screening.analysis_result.overallStatus),
                    Responses :  screening.survey_responses.map(response => {
                        return `${this.getCategoryLongName(response.category.l10nKey)}: ${response.responses.map(r => `${r.question.l10nKey }: ${this.getAnswerDisplayValue(r.answer.l10nKey)}`).join(', ')}`;
                    }).join('; ')
                });
            });

            return csvData;
        },
        async exportToPdf(screening: ScreeningResult) {
            try {
              // Generate the PDF
              const pdfBytes = await generateScreeningPdf(screening);

              // Create a Blob from the PDF bytes
              const blob = new Blob([pdfBytes], { type: 'application/pdf' });

              // Create a temporary URL for the Blob
              const url = URL.createObjectURL(blob);

              // Open the PDF in a new tab or download it
              window.open(url);

              // Clean up the URL object
              setTimeout(() => URL.revokeObjectURL(url), 10000); // Revoke after 10 seconds
            } catch (error) {
              console.error('Error generating PDF:', error);
            }
        },

        // Function to get the current week's start and end dates
        getCurrentWeekDates() {
            const currentDate = new Date();
            const firstDayOfWeek = new Date(currentDate);
            const dayOfWeek = currentDate.getDay();
            const diff = currentDate.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1); // Adjust for Sunday

            firstDayOfWeek.setDate(diff);
            firstDayOfWeek.setHours(0, 0, 0, 0);

            const lastDayOfWeek = new Date(firstDayOfWeek);
            lastDayOfWeek.setDate(lastDayOfWeek.getDate() + 6);
            lastDayOfWeek.setHours(23, 59, 59, 999);

            return {
                start: firstDayOfWeek.toISOString().split('T')[0], // Format as YYYY-MM-DD
                end: lastDayOfWeek.toISOString().split('T')[0]
            };
        },

        // Get screening trend data from API
        async fetchScreeningTrend(startDate?: string, endDate?: string) {
            this.isTrendLoading = true;
            this.trendError = null;

            // Use provided dates or default to current week
            const dates = {
                start: startDate || this.getCurrentWeekDates().start,
                end: endDate || this.getCurrentWeekDates().end
            };

            try {
                const response = await $fetch<ScreeningTrendData[]>('/organization/screening/trend', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    // query: {
                    //     start_date: dates.start,
                    //     end_date: dates.end
                    // }
                });

                this.trendData = response;
                return response;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch screening trend data';
                this.trendError = errorMessage;
                console.error('Error fetching screening trend data:', error);
                throw new Error(errorMessage);
            } finally {
                this.isTrendLoading = false;
            }
        },

        // Get top district with highest referred screening results
        async fetchTopDistrict(startDate?: string, endDate?: string): Promise<TopReferredDistrictType> {
            // Use provided dates or default to current week
            const dates = {
                start: startDate || this.getCurrentWeekDates().start,
                end: endDate || this.getCurrentWeekDates().end
            };

            try {
                const response = await $fetch<TopReferredDistrictType[]>('/organization/screening/referred/groupedBy/district', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                });

                return response[0];
            } catch (error:any) {
                console.error('Error fetching screening trend data:', error);
                throw new Error(error);
            } 
        },

         // Get top school with highest referred screening results
         async fetchTopSchool(startDate?: string, endDate?: string): Promise<TopReferredSchoolType> {
            // Use provided dates or default to current week
            const dates = {
                start: startDate || this.getCurrentWeekDates().start,
                end: endDate || this.getCurrentWeekDates().end
            };

            try {
                const response = await $fetch<TopReferredSchoolType[]>('/organization/screening/referred/groupedBy/school', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                });

                return response[0];
            } catch (error:any) {
                console.error('Error fetching screening trend data:', error);
                throw new Error(error);
            } 
        },
    },

    getters: {
        hasQuestions: (state) => state.questionsState === ApiResponseState.SUCCESS && state.questions.length > 0,
        isLoading: (state) => state.questionsState === ApiResponseState.LOADING,
        failed: (state) => state.questionsState === ApiResponseState.FAILED,
        success: (state) => state.questionsState === ApiResponseState.SUCCESS,
        hasResults: (state) => state.resultsState === ApiResponseState.SUCCESS && state.screeningResults.length > 0,
        resultsSuccess: (state) => state.resultsState === ApiResponseState.SUCCESS,
        hasTrendData: (state) => state.trendData.length > 0,
        trendCategories: (state) => {
            if (!state.trendData || state.trendData.length === 0) {
                return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            }

            return state.trendData.map(item => {
                // Format date for display (e.g., "Apr 02")
                const date = new Date(item.screening_date);
                return date.toLocaleDateString('en-US', { month: 'short', day: '2-digit' });
            });
        },
        trendValues: (state) => {
            if (!state.trendData || state.trendData.length === 0) {
                return [0, 0, 0, 0, 0, 0, 0];
            }

            return state.trendData.map(item => item.screening_count);
        }
    }
})
