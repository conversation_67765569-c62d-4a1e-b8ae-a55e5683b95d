import { defineStore } from 'pinia'
import { useUserStore } from '../auth/user/user.store'
import { ApiResponseState } from '~/utils/enum/apiResponse.enum'

interface SchoolType {
    id: string
    name: string
}

interface Location {
    id: string
    name: string
}

interface Country extends Location {
    country_code: string
}

export interface School {
    id: string
    name: string
    type: SchoolType
    last_screening_date: string | null
    country: Country
    region: Location
    district: Location
    organisationId: string
    avatarUrl: string
    total_children_count: number
}

interface SchoolsResponse {
    data: School[]
    page: number
    limit: number
    count: number
}

interface SchoolsState {
    schools: School[]
    selectedSchools: School[]
    selectedSchool: School | null
    currentPage: number
    totalPages: number
    totalItems: number
    isLoading: boolean
    error: string | null
    apiState: ApiResponseState
}

export interface CreateSchoolPayload {
    name: string;
    type_id: number;
    district_id: string;
    organization_id: string;
}

export const useSchoolsStore = defineStore('schools', {
    state: (): SchoolsState => ({
        schools: [],
        selectedSchools: [],
        selectedSchool: null,
        currentPage: 1,
        totalPages: 0,
        totalItems: 0,
        isLoading: false,
        error: null,
        apiState: ApiResponseState.NULL
    }),

    actions: {
        async fetchAllSchools(page = 1, limit = 20) {
            this.isLoading = true
            this.apiState = ApiResponseState.LOADING

            try {
                const response = await $fetch<SchoolsResponse>('/school/', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    params: {
                        page,
                        limit
                    }
                })

                this.schools = response.data
                this.currentPage = response.page
                this.totalItems = response.count
                this.totalPages = Math.ceil(response.count / limit)
                this.apiState = ApiResponseState.SUCCESS
                return response
            } catch (error) {
                console.log(error)
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch schools'
                this.error = errorMessage
                this.apiState = ApiResponseState.FAILED
                console.error('Error fetching schools:', error)
                throw new Error(errorMessage)
            }
        },

        async createSchool(payload: CreateSchoolPayload) {
            console.log(payload)
            try {
                const response = await $fetch<any>('/school/', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    body: payload
                })
                return response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create school'
                this.error = errorMessage
                console.error('Error creating school:', error)
                throw new Error(errorMessage)
            }
        },

        async bulkCreateSchool(payload: CreateSchoolPayload[]) {

            try {
                const response = await $fetch('/school/bulk', {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    body: { schools: payload }
                })

                return response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create schools'
                this.error = errorMessage
                console.error('Error creating schools:', error)
                throw new Error(errorMessage)
            }
        },

        async deleteSchool(schoolId: string) {
            try {
                const response = await $fetch<any>(`/school/${schoolId}`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    }
                })

                return true
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete school and associated records'
                this.error = errorMessage
                console.error('Failed to delete school and associated records:', error)
                throw new Error(errorMessage)
            }
        },

        async deleteMultipleSchools(schoolIds: string[]) {
            try {
                const response = await $fetch<any>(`/school/bulk/`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${useUserStore().token}`
                    },
                    body: { schoolIds }
                })

                return true
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete schools and associated records'
                this.error = errorMessage
                console.error('Failed to delete schools and associated records:', error)
                throw new Error(errorMessage)
            }
        },
        selectSchools(schools: School[]) {
            this.selectedSchools = schools
        },

        selectSchool(school: School) {
            this.selectedSchool = school
        },

        resetSelectedSchools() {
            this.selectedSchools = []
            this.selectedSchool = null
        },
        exportToCSV() {
            return useExportToCSV({
                fileName: `Disability_Detect_Schools_${Date.now()}`,
                header: [
                    "ID",
                    "Name",
                    "Type",
                    "Last Screening Date",
                    "Total Children",
                    "District",
                    "Region",
                    "Country"
                ],
                records: this.transformSchoolsToCSVData()
            });
        },
        transformSchoolsToCSVData(): any[] {
            const csvData: any[] = [];

            const schoolData = this.selectedSchools.length > 0 ? this.selectedSchools : this.schools;

            // Transform each school object into the desired format
            schoolData.forEach(school => {
                const {
                    id,
                    name,
                    type,
                    last_screening_date,
                    total_children_count,
                    district,
                    region,
                    country
                } = school;

                // Push the transformed values into the CSV data array
                csvData.push({
                    ID: id,
                    Name: name,
                    Type: type?.name || "N/A",
                    "Last Screening Date": last_screening_date || "N/A",
                    "Total Children": total_children_count,
                    District: district?.name || "N/A",
                    Region: region?.name || "N/A",
                    Country: country?.name || "N/A"
                });
            });

            return csvData;
        },
    },

    getters: {
        hasSchools: (state): boolean => state.schools.length > 0,
        hasError: (state): boolean => state.apiState === ApiResponseState.FAILED && !!state.error
    }
})