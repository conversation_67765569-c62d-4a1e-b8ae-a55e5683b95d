import { defineStore } from 'pinia'
import { useUserStore } from '../auth/user/user.store'
import { ApiResponseState } from '~/utils/enum/apiResponse.enum'

export interface Role {
    id: number
    name: string
    description?: string
}

export interface Agent {
    id: string
    first_name: string
    last_name: string
    email: string
    avatar_url: string
    role: {
        id: number
        name: string
    }
}

interface AgentsResponse {
    data: Agent[]
    page: number
    limit: number
    count: number
}

interface AgentsState {
    agents: Agent[]
    selectedAgents: Agent[]
    roles: Role[]
    currentPage: number
    totalPages: number
    totalItems: number
    isLoading: boolean
    isLoadingRoles: boolean
    error: string | null
    apiState: ApiResponseState
    rolesApiState: ApiResponseState
}

export const useAgentsStore = defineStore('agents', {
    state: (): AgentsState => ({
        agents: [],
        selectedAgents: [],
        roles: [],
        currentPage: 1,
        totalPages: 0,
        totalItems: 0,
        isLoading: false,
        isLoadingRoles: false,
        error: null,
        apiState: ApiResponseState.NULL,
        rolesApiState: ApiResponseState.NULL
    }),

    actions: {
        async fetchAgents(page = 1, limit = 10) {

            try {
                const userStore = useUserStore()
                const organizationId = userStore.currentUser?.organization?.id

                if (!organizationId) {
                    throw new Error('Organization ID not found')
                }

                const response = await $fetch<AgentsResponse>(`/organization/${organizationId}/agents`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${userStore.token}`
                    },
                    query: {
                        page,
                        limit
                    }
                })

                return response.data
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch agents'
                console.error('Error fetching agents:', error)
                throw new Error(errorMessage)
            } 
        },

        selectAgents(agents: Agent[]) {
            this.selectedAgents = agents
        },

        resetSelectedAgents() {
            this.selectedAgents = []
        },

        exportToCSV() {
            const agentsToExport = this.selectedAgents.length > 0 ? this.selectedAgents : this.agents
            return useExportToCSV({
                fileName: `Agents_${Date.now()}.csv`,
                header: ["ID", "First Name", "Last Name", "Email", "Role"],
                records: this.transformAgentsToCSVData(agentsToExport)
            })
        },

        transformAgentsToCSVData(agentsData?: Agent[]): string[][] {
            const dataToExport = agentsData || this.agents
            return dataToExport.map(agent => [
                agent.id,
                agent.first_name,
                agent.last_name,
                agent.email,
                agent.role.name
            ])
        },

        getAgentFullName(agent: Agent): string {
            return `${agent.first_name} ${agent.last_name}`.trim()
        },

        getAgentInitials(agent: Agent): string {
            return `${agent.first_name.charAt(0)}${agent.last_name.charAt(0)}`.toUpperCase()
        },

        async fetchRoles() {
            this.isLoadingRoles = true
            this.rolesApiState = ApiResponseState.LOADING

            try {
                const userStore = useUserStore()
                const organizationId = userStore.currentUser?.organization?.id

                if (!organizationId) {
                    throw new Error('Organization ID not found')
                }

                const response = await $fetch<Role[]>(`/organization/${organizationId}/roles`, {
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${userStore.token}`
                    }
                })

                this.roles = response
                this.rolesApiState = ApiResponseState.SUCCESS
                return response
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch roles'
                this.error = errorMessage
                this.rolesApiState = ApiResponseState.FAILED
                console.error('Error fetching roles:', error)
                throw new Error(errorMessage)
            } finally {
                this.isLoadingRoles = false
            }
        },

        async updateAgentRole(agentId: string, roleId: number) {
            try {
                const userStore = useUserStore()
                const organizationId = userStore.currentUser?.organization?.id

                if (!organizationId) {
                    throw new Error('Organization ID not found')
                }

                await $fetch(`/organization/agent/${agentId}/role`, {
                    method: 'PUT',
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${userStore.token}`,
                        'Content-Type': 'application/json'
                    },
                    body: {
                        role_id: roleId
                    }
                })

                // Update the agent's role in the local state
                const agentIndex = this.agents.findIndex(agent => agent.id === agentId)
                if (agentIndex !== -1) {
                    const role = this.roles.find(r => r.id === roleId)
                    if (role) {
                        this.agents[agentIndex].role = { id: role.id, name: role.name }
                    }
                }

                return true
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to update agent role'
                console.error('Error updating agent role:', error)
                throw new Error(errorMessage)
            }
        },

        async updateMultipleAgentRoles(agentIds: string[], roleId: number) {
            try {
                const userStore = useUserStore()
                const organizationId = userStore.currentUser?.organization?.id

                if (!organizationId) {
                    throw new Error('Organization ID not found')
                }

                await $fetch(`/organization/agent/role`, {
                    method: 'PUT',
                    baseURL: useRuntimeConfig().public.API_BASE_URL,
                    headers: {
                        'Authorization': `Bearer ${userStore.token}`,
                        'Content-Type': 'application/json'
                    },
                    body: {
                        agent_ids: agentIds,
                        role_id: roleId
                    }
                })

                // Update the agents' roles in the local state
                const role = this.roles.find(r => r.id === roleId)
                if (role) {
                    agentIds.forEach(agentId => {
                        const agentIndex = this.agents.findIndex(agent => agent.id === agentId)
                        if (agentIndex !== -1) {
                            this.agents[agentIndex].role = { id: role.id, name: role.name }
                        }
                    })
                }

                return true
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to update agent roles'
                console.error('Error updating agent roles:', error)
                throw new Error(errorMessage)
            }
        }
    },

    getters: {
        hasAgents: (state): boolean => state.apiState === ApiResponseState.SUCCESS && state.agents.length > 0,
        isLoadingAgents: (state): boolean => state.apiState === ApiResponseState.LOADING,
        agentsSuccess: (state): boolean => state.apiState === ApiResponseState.SUCCESS,
        agentsFailed: (state): boolean => state.apiState === ApiResponseState.FAILED,
        hasRoles: (state): boolean => state.rolesApiState === ApiResponseState.SUCCESS && state.roles.length > 0,
        rolesSuccess: (state): boolean => state.rolesApiState === ApiResponseState.SUCCESS,
        rolesFailed: (state): boolean => state.rolesApiState === ApiResponseState.FAILED
    }
})
