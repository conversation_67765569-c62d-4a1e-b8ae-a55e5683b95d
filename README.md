# CFC Disability Detection Dashboard

A comprehensive web application built with Nuxt 3 for managing and tracking disability screenings in children. This dashboard helps organizations efficiently manage children's data, conduct screenings, and track results across different schools.

## Key Features

- **Child Management**
  - Add individual or bulk children records
  - Track screening status and history
  - Filter children by gender and screening status

- **Screening Process**
  - Structured disability assessment workflow
  - Real-time disability status tracking
  - Comprehensive screening questionnaires

- **School Management**
  - School registration and tracking
  - Bulk school data import
  - School-wise screening analytics

- **User Management**
  - Role-based access control
  - Organization management
  - Agent tracking and permissions

## Tech Stack

- **Frontend Framework:** Nuxt 3
- **UI Components:** Custom components with Tailwind CSS
- **State Management:** Nuxt Store
- **Charts:** ApexCharts
- **Authentication:** Built-in auth system

## Setup

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory with required environment variables:
```env
# Add your environment variables here
```

4. Start the development server:
```bash
npm run dev
```

## Build for Production

```bash
# Build the application
npm run build

# Preview the production build
npm run preview
```

## Project Structure

- `/components` - Reusable Vue components
- `/pages` - Application routes and views
- `/stores` - State management
- `/composables` - Reusable composition functions
- `/utils` - Helper functions and utilities
- `/middleware` - Route middleware (auth, etc.)

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request

## License

[Add License Information]
