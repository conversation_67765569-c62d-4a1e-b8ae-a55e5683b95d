import questionsData from './questions.json';

export function getQuestion(l10nkey, placeholders = {}) {
  const question = questionsData[l10nkey];

  if (!question) {
    throw new Error(`Question with key "${l10nkey}" not found.`);
  }

  // Replace placeholders in the question
  let formattedQuestion = question;
  for (const [placeholder, value] of Object.entries(placeholders)) {
    const regex = new RegExp(`\\{${placeholder}\\}`, 'g');
    formattedQuestion = formattedQuestion.replace(regex, value);
  }

  return formattedQuestion;
}