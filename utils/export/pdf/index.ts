import { PDFDocument, StandardFonts , rgb} from 'pdf-lib';
import {type ScreeningResult} from '@/stores/screening/screening.store'
import { getQuestion } from '~/utils/questions';


export async function generateScreeningPdf(screeningData:ScreeningResult) {
  // Step 1: Create a new PDF document
  const pdfDoc = await PDFDocument.create();

  // Step 2: Add a page to the PDF
  const page = pdfDoc.addPage();
  const { width, height } = page.getSize();

  // Step 3: Set up fonts
  const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  // Step 4: Define helper functions for drawing text
  function drawHeader(page, x, y, text, font, size, color) {
    page.drawText(text, {
      x,
      y,
      font,
      size,
      color,
    });
  }

  function drawSectionTitle(page, x, y, title) {
    page.drawText(title, {
      x,
      y,
      font: helveticaBoldFont,
      size: 16,
      color: rgb(0, 0, 0),
    });
  }

  function drawSubsectionTitle(page, x, y, title) {
    page.drawText(title, {
      x,
      y,
      font: helveticaBoldFont,
      size: 14,
      color: rgb(0, 0, 0),
    });
  }

  function drawText(page, x, y, text, size = 12) {
    page.drawText(text, {
      x,
      y,
      font: helveticaFont,
      size,
      color: rgb(0, 0, 0),
    });
  }

  function drawActionBadge(page, x, y, text, backgroundColor, textColor) {
    const badgeWidth = 150;
    const badgeHeight = 20;

    // Draw background rectangle
    page.drawRectangle({
      x,
      y,
      width: badgeWidth,
      height: badgeHeight,
      color: backgroundColor,
    });

    // Draw text inside the badge
    page.drawText(text, {
      x: x + 10, // Padding
      y: y + badgeHeight / 2,
      font: helveticaBoldFont,
      size: 10,
      color: textColor,
    });
  }

  // Step 5: Draw the header section
  const childName = `${screeningData.child_id.first_name} ${screeningData.child_id.last_name}`;
  const screenerName = `${screeningData.created_by.first_name} ${screeningData.created_by.last_name}`;
  const screeningDate = new Date(screeningData.created_at).toLocaleDateString();

  drawHeader(page, 50, height - 50, `Child: ${childName}`, helveticaBoldFont, 18, rgb(0, 0, 0));
  drawHeader(page, 50, height - 70, `ID: ${screeningData.id}`, helveticaFont, 12, rgb(0, 0, 0));
  drawHeader(page, width - 200, height - 50, `Screener: ${screenerName}`, helveticaFont, 12, rgb(0, 0, 0));
  drawHeader(page, width - 200, height - 70, `Date Screened: ${screeningDate}`, helveticaFont, 12, rgb(0, 0, 0));

  // Step 6: Draw Disability Status section
  const disabilityStatus = "Potential Single Disability";
  const indicatorsDetected = `${screeningData.analysis_result.rfaCount + screeningData.analysis_result.rfmCount} indicators detected`;

  drawSectionTitle(page, 50, height - 150, "Disability Status");
  drawText(page, 50, height - 170, "Comprehensive evaluation result");

  drawHeader(page, width - 200, height - 150, disabilityStatus, helveticaBoldFont, 14, rgb(1, 0, 0));
  drawHeader(page, width - 200, height - 170, indicatorsDetected, helveticaFont, 12, rgb(1, 0, 0));

  // Step 7: Draw Analysis Results section
  drawSectionTitle(page, 50, height - 250, "Analysis Results");
  drawText(page, 50, height - 270, "Detailed analysis of screening results");

  let yPosition = height - 300;

  for (const [category, result] of Object.entries(screeningData.analysis_result.categoryResults)) {
    drawSubsectionTitle(page, 50, yPosition, category);
    drawActionBadge(
      page,
      width - 200,
      yPosition + 5,
      result,
      rgb(0.9, 0.9, 0.9), // Background color
      rgb(0, 0, 0)        // Text color
    );

    yPosition -= 30;
  }

  // Step 8: Draw Survey Responses section
  drawSectionTitle(page, 50, yPosition - 50, "Survey Responses");

  for (const response of screeningData.survey_responses) {
    const category = response.category.l10nKey;
    const categoryName = getCategoryLongName(category); // Replace with your logic to get category name

    drawSubsectionTitle(page, 50, yPosition - 80, categoryName);

    for (const innerResponse of response.responses) {
      const question = getQuestion(innerResponse.question.l10nKey, { child: screeningData.child_id.first_name });
      const answer = getAnswerDisplayValue(innerResponse.answer.l10nKey);

      drawText(page, 50, yPosition - 100, question);
      drawText(page, width - 200, yPosition - 100, answer);

      yPosition -= 30;
    }
  }

  // Step 9: Serialize the PDF to bytes
  const pdfBytes = await pdfDoc.save();

  return pdfBytes;
}

function getCategoryLongName(categoryKey: string): string {
  const categoryMap: Record<string, string> = {
      'categoryVision': 'Vision',
      'categoryLiteracy': 'Literacy & Numeracy Skills',
      'categoryInattention': 'Inattention, Hyperactivity & Impulsivity',
      'categoryHearing': 'Hearing',
      'categoryMobility': 'Mobility or Motor Skills',
      'categorySocialInteraction': 'Social Interaction Skills',
      'categoryCommunication': 'Communication Skills'
  }
  
  return categoryMap[categoryKey] || 'Unknown Category'
}

function getAnswerDisplayValue(l10nKey: string): string {
  const answerMap: Record<string, string> = {
      'answerYes': 'Yes',
      'answerNo': 'No',
      'answerNoDifficulty': 'No difficulty',
      'answerSomeDifficulty': 'Some difficulty',
      'answerALotOfDifficulty': 'A lot of difficulty',
      'answerCannotDoAtAll': 'Cannot do it at all',
      'answerNever': 'Never',
      'answerOften': 'Often',
      'answerSometimes': 'Sometimes',
      'answerAlways': 'Always'
  }
  
  return answerMap[l10nKey] || l10nKey
}